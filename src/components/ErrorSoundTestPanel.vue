<template>
  <div class="error-sound-test-panel">
    <h3>错误语音播放测试面板</h3>

    <div class="test-section">
      <h4>基础测试场景</h4>
      <div class="button-group">
        <button @click="testOverlap" :disabled="isPlaying">
          测试快速连续推送
        </button>
        <button @click="testInterruption" :disabled="isPlaying">
          测试播放中被打断
        </button>
        <button @click="testNormal" :disabled="isPlaying">
          测试正常播放
        </button>
        <button @click="testSingle" :disabled="isPlaying">
          测试单个错误
        </button>
        <button @click="stopPlayback" :disabled="!isPlaying">
          停止播放
        </button>
      </div>
    </div>

    <div class="test-section">
      <h4>智能防抖测试 🚀</h4>
      <div class="button-group">
        <button @click="testSmartDebounce" :disabled="isPlaying" class="smart-btn">
          防抖机制测试
        </button>
        <button @click="testDebounceScenarios" :disabled="isPlaying" class="smart-btn">
          多场景测试
        </button>
        <button @click="testOperationLog" :disabled="isPlaying" class="smart-btn">
          操作日志测试
        </button>
        <button @click="testRapidDuplicate" :disabled="isPlaying" class="smart-btn">
          快速重复测试
        </button>
        <button @click="testSimpleDebounce" :disabled="isPlaying" class="debug-btn">
          简单测试
        </button>
      </div>
      <div class="test-description">
        <p><strong>防抖测试说明：</strong></p>
        <ul>
          <li><strong>防抖机制：</strong>模拟快速连续推送，验证300ms防抖延迟</li>
          <li><strong>多场景：</strong>测试不同错误、相同错误重复、正常间隔</li>
          <li><strong>操作日志：</strong>模拟真实操作日志推送场景</li>
          <li><strong>快速重复：</strong>测试相同错误的去重机制</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h4>声音类型</h4>
      <div class="radio-group">
        <label>
          <input type="radio" v-model="soundType" value="rational" />
          理性语音
        </label>
        <label>
          <input type="radio" v-model="soundType" value="lively" />
          活泼语音
        </label>
      </div>
    </div>

    <div class="status-section">
      <h4>播放状态</h4>
      <div class="status-info">
        <p>当前状态: <span :class="{ playing: isPlaying, stopped: !isPlaying }">
          {{ isPlaying ? '播放中' : '已停止' }}
        </span></p>
        <p>当前队列长度: {{ currentQueueLength }}</p>
        <p>当前播放索引: {{ currentPlayingIndex }}</p>
      </div>
    </div>

    <div class="log-section">
      <h4>测试日志</h4>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-btn">清空日志</button>
    </div>

    <div class="info-section">
      <h4>测试数据说明</h4>
      <div class="test-data-info">
        <div class="batch-info">
          <h5>第一批错误（立即推送）:</h5>
          <ul>
            <li>1: 双手持枪 - 未双手作业</li>
            <li>2: 垂直作业面 - 未垂直作业面</li>
            <li>3: 拧紧贴合 - 拧紧未贴合</li>
          </ul>
        </div>
        <div class="batch-info">
          <h5>第二批错误（500ms后推送）:</h5>
          <ul>
            <li>4: 扳手绿灯 - 拧紧枪未亮绿灯</li>
            <li>8: 手套识别 - 未戴手套</li>
            <li>21: 堵盖大小判定 - 堵盖选择错误</li>
          </ul>
        </div>
        <div class="batch-info">
          <h5>第三批错误（1000ms后推送，最终播放）:</h5>
          <ul>
            <li>22: 堵盖贴合判定 - 堵盖不贴合</li>
            <li>31: 胶条安装判定 - 安装不规范</li>
            <li>41: 油管距离判定 - 油管未闭合</li>
            <li>51: 线束插接-手部回拔判定 - 安装完成未回拔</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useErrorSound, type SoundType } from '/@/composables/useErrorSound';
import { useSmartErrorSound } from '/@/composables/useSmartErrorSound';
import {
  testErrorSoundOverlap,
  testErrorSoundNormal,
  testErrorSoundInterruption,
  testSingleError
} from '/@/test/errorSoundTest';

const { isPlaying, stopCurrentPlayback } = useErrorSound();
const { playErrorSoundSmart } = useSmartErrorSound();
const soundType = ref<SoundType>('rational');
const logs = ref<Array<{ time: string, message: string }>>([]);

// 模拟播放状态（实际应该从 useErrorSound 获取）
const currentQueueLength = ref(0);
const currentPlayingIndex = ref(0);

const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message });
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

const testOverlap = async () => {
  addLog('开始测试快速连续推送');
  await testErrorSoundOverlap();
  addLog('快速连续推送测试完成 - 应该只播放最后一批错误');
};

const testInterruption = async () => {
  addLog('开始测试播放中被打断');
  await testErrorSoundInterruption();
  addLog('播放中被打断测试完成 - 新错误应该立即打断当前播放');
};

const testNormal = async () => {
  addLog('开始测试正常播放（间隔足够）');
  await testErrorSoundNormal();
  addLog('正常播放测试完成 - 每批错误都会完整播放');
};

const testSingle = () => {
  addLog('测试单个错误播放');
  testSingleError();
  addLog('单个错误测试完成');
};

// 新增的智能防抖测试函数
const testSmartDebounce = async () => {
  addLog('🚀 开始智能防抖测试');
  addLog('模拟快速连续推送相同错误（间隔100ms）');

  const sameErrors = [
    { actionType: 1 }, // 未双手作业
    { actionType: 2 }, // 未垂直作业面
    { actionType: 8 }, // 未戴手套
  ];

  addLog('第1次推送');
  playErrorSoundSmart(sameErrors, soundType.value);

  // 第二次推送（100ms后）
    addLog('第2次推送');
    playErrorSoundSmart(sameErrors, soundType.value);

  // 第三次推送（200ms后）
    addLog('第3次推送');
    playErrorSoundSmart(sameErrors, soundType.value);

  // 第四次推送（300ms后）
  setTimeout(() => {
    addLog('第4次推送');
    playErrorSoundSmart(sameErrors, soundType.value);
  }, 100);

  // 第五次推送（800ms后，应该会播放）
  setTimeout(() => {
    addLog('第5次推送（延迟较长，应该会播放）');
    playErrorSoundSmart(sameErrors, soundType.value);
  }, 200);

  addLog('✅ 防抖测试启动完成 - 应该只播放2次（第4次和第5次）');
};

const testDebounceScenarios = async () => {
  addLog('🎯 开始多场景防抖测试');

  const errors1 = [{ actionType: 1 }, { actionType: 2 }];
  const errors2 = [{ actionType: 3 }, { actionType: 4 }];
  const errors3 = [{ actionType: 1 }, { actionType: 2 }]; // 与errors1相同

  // 场景1：快速连续推送不同错误
  addLog('=== 场景1：快速连续推送不同错误 ===');
  playErrorSoundSmart(errors1, soundType.value);

  setTimeout(() => {
    playErrorSoundSmart(errors2, soundType.value); // 不同错误，应该取消前一个
  }, 150);

  // 场景2：相同错误在短时间内重复推送
  setTimeout(() => {
    addLog('=== 场景2：相同错误重复推送 ===');
    playErrorSoundSmart(errors1, soundType.value);

    setTimeout(() => {
      playErrorSoundSmart(errors3, soundType.value); // 相同错误，应该被忽略
    }, 500);
  }, 3000);

  // 场景3：正常间隔的错误推送
  setTimeout(() => {
    addLog('=== 场景3：正常间隔的错误推送 ===');
    playErrorSoundSmart(errors1, soundType.value);

    setTimeout(() => {
      playErrorSoundSmart(errors2, soundType.value); // 间隔足够长，应该正常播放
    }, 3000);
  }, 8000);

  addLog('✅ 多场景测试启动完成 - 检查播放效果');
};

const testOperationLog = async () => {
  addLog('📋 开始操作日志测试');

  // 模拟操作日志数据
  const operationLogs = [
    {
      id: 1,
      result: 0, // 不合格
      actionLabels: [{ actionType: 1 }, { actionType: 2 }],
    },
    {
      id: 2,
      result: 1, // 合格，不应该播放
      actionLabels: [{ actionType: 3 }],
    },
    {
      id: 3,
      result: 2, // 不达标
      actionLabels: [{ actionType: 4 }, { actionType: 5 }],
    },
    {
      id: 4,
      result: 0, // 不合格，相同错误
      actionLabels: [{ actionType: 1 }, { actionType: 2 }],
    }
  ];

  // 模拟快速连续的操作日志推送
  operationLogs.forEach((log, index) => {
    setTimeout(() => {
      if ((log.result === 0 || log.result === 2) && log.actionLabels?.length) {
        addLog(`处理操作日志 ID:${log.id}, result:${log.result}`);
        playErrorSoundSmart(log.actionLabels, soundType.value);
      } else {
        addLog(`跳过操作日志 ID:${log.id}, result:${log.result}（合格或无错误）`);
      }
    }, index * 100); // 每100ms推送一条
  });

  addLog('✅ 操作日志测试启动完成 - 应该播放日志1和3，跳过日志2，去重日志4');
};

const testRapidDuplicate = async () => {
  addLog('⚡ 开始快速重复测试');

  const sameErrors = [
    { actionType: 1 }, // 未双手作业
    { actionType: 2 }, // 未垂直作业面
    { actionType: 8 }, // 未戴手套
  ];

  addLog('模拟快速连续推送相同错误（间隔100ms）');

  // 快速连续推送相同错误
  for (let i = 0; i < 5; i++) {
    setTimeout(() => {
      addLog(`第${i + 1}次推送相同错误`);
      playErrorSoundSmart(sameErrors, soundType.value);
    }, i * 100);
  }

  addLog('✅ 快速重复测试启动完成 - 应该只播放最后一次');
};

// 简单测试函数，用于调试
const testSimpleDebounce = () => {
  addLog('🔧 开始简单测试');

  const testErrors = [{ actionType: 1 }, { actionType: 8 }];

  try {
    addLog('调用 playErrorSoundSmart...');
    console.log('🔧 调用参数:', testErrors, soundType.value);

    playErrorSoundSmart(testErrors, soundType.value);

    addLog('✅ 简单测试调用完成');
  } catch (error: any) {
    addLog(`❌ 简单测试失败: ${error?.message || error}`);
    console.error('🔧 错误详情:', error);
  }
};

const stopPlayback = () => {
  stopCurrentPlayback();
  addLog('手动停止播放');
};

const clearLogs = () => {
  logs.value = [];
};

onMounted(() => {
  addLog('错误语音测试面板已加载');
  addLog('📋 基础测试：重叠测试会在短时间内推送3批错误，最终只播放最后一批');
  addLog('🚀 智能防抖测试：新增防抖、去重、多场景测试功能');
  addLog('💡 提示：智能测试会在控制台显示详细的彩色日志');

  // 测试智能播放功能是否可用
  console.log('🔧 调试信息：');
  console.log('playErrorSoundSmart 函数:', playErrorSoundSmart);
  console.log('soundType:', soundType.value);
});
</script>

<style scoped>
.error-sound-test-panel {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.test-section, .status-section, .log-section, .info-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.smart-btn {
  background-color: #28a745;
  position: relative;
}

.smart-btn:hover:not(:disabled) {
  background-color: #218838;
}

.smart-btn::before {
  content: '🚀';
  margin-right: 5px;
}

.debug-btn {
  background-color: #ffc107;
  color: #212529;
}

.debug-btn:hover:not(:disabled) {
  background-color: #e0a800;
}

.debug-btn::before {
  content: '🔧';
  margin-right: 5px;
}

.test-description {
  margin-top: 15px;
  padding: 10px;
  background-color: #e8f5e8;
  border-radius: 5px;
  border-left: 4px solid #28a745;
}

.test-description p {
  margin: 0 0 10px 0;
  font-weight: bold;
  color: #155724;
}

.test-description ul {
  margin: 0;
  padding-left: 20px;
}

.test-description li {
  margin-bottom: 5px;
  color: #155724;
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.status-info p {
  margin: 5px 0;
}

.playing {
  color: #28a745;
  font-weight: bold;
}

.stopped {
  color: #6c757d;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 14px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  color: #333;
}

.clear-btn {
  background-color: #dc3545;
}

.clear-btn:hover:not(:disabled) {
  background-color: #c82333;
}

.test-data-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.batch-info {
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  border-left: 4px solid #007bff;
}

.batch-info h5 {
  margin: 0 0 10px 0;
  color: #007bff;
}

.batch-info ul {
  margin: 0;
  padding-left: 20px;
}

.batch-info li {
  margin-bottom: 5px;
  font-size: 14px;
}

h3, h4, h5 {
  margin-top: 0;
}
</style>
