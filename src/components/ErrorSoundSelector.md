# ErrorSoundSelector 组件使用说明

## 概述

`ErrorSoundSelector` 是一个专门用于测试错误语音播放功能的 Vue 组件。它提供了直观的界面来选择不同的错误类型，切换声音风格，并测试语音播放功能。

## 功能特点

### 🎵 双声音风格
- **理性语音**：专业、严肃的语音风格
- **活泼语音**：轻松、友好的语音风格
- 支持实时切换，无需重新加载页面

### 📂 分类管理
错误按照作业类型进行分类：
- **拧紧作业**：双手持枪、垂直作业面、拧紧贴合、扳手绿灯
- **堵盖检测**：堵盖大小判定、堵盖贴合判定、堵盖手部按压判定
- **胶条检测**：胶条安装判定、胶条手部按压判定、胶条结果判定
- **管线检测**：油管距离判定、管路距离判定、管线手部回拔判定
- **线束插接**：线束插接手部回拔判定、线束插接安装结果
- **基础检测**：手套识别

### 🔄 智能播放
- 支持单个错误播放和批量播放
- 新错误推送会自动打断当前播放
- 依次播放多个错误，确保每个错误都能完整播放

### 📊 实时状态
- 显示当前播放状态（播放中/已停止）
- 显示正在播放的错误信息
- 显示播放进度（当前播放第几个/总共几个）

## 使用方法

### 基本使用

```vue
<template>
  <ErrorSoundSelector />
</template>

<script setup>
import ErrorSoundSelector from '@/components/ErrorSoundSelector.vue';
</script>
```

### 在首页中使用

组件已经集成到首页中，可以直接在首页看到和使用。

### 独立演示页面

访问 `ErrorSoundDemo.vue` 页面可以看到完整的演示和使用说明。

## 操作指南

### 1. 选择错误类型
- 点击"选择错误类型"下拉菜单
- 选择"全部类型"查看所有分类的概览
- 选择具体类型（如"拧紧作业"）查看该类型下的所有错误

### 2. 切换声音风格
- 使用"理性语音/活泼语音"下拉菜单切换声音类型
- 点击"测试当前声音类型"按钮听到当前选择的声音风格

### 3. 播放错误语音
- **播放单个错误**：在错误列表中点击某个错误的"播放"按钮
- **播放所有错误**：点击"播放该类型所有错误"按钮依次播放该类型下的所有错误

### 4. 控制播放
- **停止播放**：点击"停止播放"按钮立即停止当前播放
- **查看状态**：底部状态栏显示播放状态和进度信息

## 控制台输出

组件会在浏览器控制台输出详细的日志信息：

```javascript
// 切换类型时
切换到类型: 拧紧作业
该类型下有 4 个错误

// 切换声音类型时
切换声音类型: 理性语音

// 播放所有错误时
开始播放 拧紧作业 类型的所有错误
共 4 个错误将依次播放:
  1. 1: 双手持枪 - 未双手作业
  2. 2: 垂直作业面 - 未垂直作业面
  3. 3: 拧紧贴合 - 拧紧未贴合
  4. 4: 扳手绿灯 - 拧紧枪未亮绿灯

// 播放完成时
拧紧作业 类型的所有错误播放完成！
```

## 错误类型详情

### 拧紧作业（4个错误）
- `1` - 双手持枪：未双手作业
- `2` - 垂直作业面：未垂直作业面
- `3` - 拧紧贴合：拧紧未贴合
- `4` - 扳手绿灯：拧紧枪未亮绿灯

### 堵盖检测（3个错误）
- `21` - 堵盖大小判定：堵盖选择错误
- `22` - 堵盖贴合判定：堵盖不贴合
- `23` - 堵盖手部按压判定：堵盖未按压

### 胶条检测（3个错误）
- `31` - 胶条安装判定：安装不规范
- `32` - 胶条手部按压判定：按压不规范
- `33` - 胶条结果判定：安装不贴合

### 管线检测（3个错误）
- `41` - 油管距离判定：油管未闭合
- `42` - 管路距离判定：插拔管未闭合
- `43` - 管线手部回拔判定：安装完成未回拔

### 线束插接（2个错误）
- `51` - 线束插接手部回拔判定：安装完成未回拔
- `52` - 线束插接安装结果：安装不规范

### 基础检测（1个错误）
- `8` - 手套识别：未戴手套

## 技术实现

### 依赖
- `useErrorSound` composable：核心语音播放逻辑
- Ant Design Vue：UI 组件库
- Vue 3 Composition API：响应式状态管理

### 核心功能
- 错误分类和过滤
- 声音类型切换
- 播放状态管理
- 用户交互处理

### 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 触摸友好的交互

## 注意事项

1. **浏览器音频策略**：某些浏览器需要用户交互后才能播放音频
2. **音频文件路径**：确保音频文件存在于正确的路径
3. **网络连接**：音频文件需要网络加载，请确保网络连接正常
4. **音量设置**：建议调整到合适的音量或佩戴耳机
5. **性能考虑**：大量音频文件可能影响加载性能

## 故障排除

### 音频无法播放
1. 检查浏览器是否允许音频播放
2. 确认音频文件路径是否正确
3. 检查网络连接是否正常
4. 尝试用户交互后再播放

### 切换类型无响应
1. 检查控制台是否有错误信息
2. 确认组件是否正确导入
3. 检查 useErrorSound 是否正常工作

### 播放状态异常
1. 尝试手动停止播放后重新开始
2. 刷新页面重新初始化
3. 检查浏览器控制台的错误信息
