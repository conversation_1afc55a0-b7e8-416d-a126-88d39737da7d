<template>
  <div class="simple-camera-box">
    <div class="title">{{ title }}</div>
    <div class="video-container">
      <div v-if="showStatusTag" class="status-tag" :class="statusClass"></div>
      <video :id="videoId" autoplay playsinline muted></video>
      <div v-if="isConnecting" class="camera-loading">
        <a-spin tip="摄像头连接中..." />
      </div>
      <!-- 连接失败提示 -->
      <div v-if="connectionFailed" class="connection-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">摄像头连接失败</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { createLocalStorage } from '/@/utils/cache';
import { V1OpenApiGetUrlStream_channel } from '/@/api/cddc.req';
import WebRtcStreamer from '/@/utils/helper/webrtcstreamer';
import { useGlobSetting } from '/@/hooks/setting';

export default defineComponent({
  name: 'SimpleCameraView',
  props: {
    title: {
      type: String,
      default: '摄像头',
    },
    showStatusTag: {
      type: Boolean,
      default: false,
    },
    statusResult: {
      type: Number,
      default: -1,
    },
  },
  emits: ['connected', 'disconnected', 'error'],
  setup(props, { emit }) {
    const isConnecting = ref(false);
    const isTimeout = ref(false);
    const connectionFailed = ref(false);
    const retryCount = ref(0);
    const maxRetries = 3;
    const videoId = `simple-camera-${Math.random().toString(36).substring(2, 11)}`;
    let webRtcServer: InstanceType<typeof WebRtcStreamer> | null = null;
    let retryTimer: number | null = null;
    let connectionTimeoutTimer: number | null = null;
    let { webrtcUrl } = useGlobSetting();
    let videoEventHandlers: { playing?: () => void; error?: (event: any) => void } = {};
    const isUnmounted = ref(false); // 1. 新增卸载标记

    const statusClass = computed(() => {
      switch (props.statusResult) {
        case 1:
          return 'success';
        case 0:
          return 'error';
        case 2:
          return 'warning';
        default:
          return '';
      }
    });

    const cleanupVideoConnection = () => {
      const videoElement = document.getElementById(videoId) as HTMLVideoElement;
      if (videoElement) {
        // 移除事件监听器
        if (videoEventHandlers.playing) {
          videoElement.removeEventListener('playing', videoEventHandlers.playing);
        }
        if (videoEventHandlers.error) {
          videoElement.removeEventListener('error', videoEventHandlers.error);
        }

        // 停止视频流
        videoElement.pause();
        videoElement.srcObject = null;
        videoElement.src = '';
        videoElement.load();
      }

      // 关闭WebRTC连接
      if (webRtcServer) {
        try {
          webRtcServer.disconnect();
        } catch (error) {
          console.warn('WebRTC断开连接时出错:', error);
        }
        webRtcServer = null;
      }

      // 重置事件处理器
      videoEventHandlers = {};
    };

    const handleConnectionFailure = () => {
      // 3. 检查标记，如果已卸载则不执行任何操作
      if (isUnmounted.value) return;
      console.log(`摄像头连接失败 (尝试 ${retryCount.value + 1}/${maxRetries})`);

      isConnecting.value = false;

      if (retryCount.value < maxRetries - 1) {
        retryCount.value++;
        console.log(`${3000}ms后重试连接...`);
        retryTimer = window.setTimeout(() => {
          getRtspUrl(true);
        }, 3000);
      } else {
        console.log('摄像头连接重试次数已达上限，停止重试');
        connectionFailed.value = true;
        emit('error');
      }
    };

    const getRtspUrl = async (isRetry = false) => {
      try {
        if (retryTimer) clearTimeout(retryTimer);
        if (connectionTimeoutTimer) clearTimeout(connectionTimeoutTimer);
        retryTimer = null;
        connectionTimeoutTimer = null;

        // 3. 在开始任务前检查标记
        if (isUnmounted.value) {
          console.log('组件已卸载，取消连接尝试。');
          return;
        }

        if (!isRetry) retryCount.value = 0;

        isConnecting.value = true;
        isTimeout.value = false;
        connectionFailed.value = false;

        console.log(`简约版摄像头开始连接 (尝试 ${retryCount.value + 1}/${maxRetries})`);

        const ls = createLocalStorage();
        const locationId = ls.get('locationId');
        if (!locationId) {
          message.error('缺少工位ID');
          handleConnectionFailure();
          return;
        }

        const streamUrl = await V1OpenApiGetUrlStream_channel({ channel: locationId });
        // 3. 在异步操作后检查标记
        if (isUnmounted.value) {
          console.log('组件已卸载，获取地址后取消连接。');
          return;
        }
        console.log('获取视频流地址:', streamUrl);

        if (!streamUrl || typeof streamUrl !== 'string') {
          console.error('获取视频流地址失败：返回值无效', streamUrl);
          handleConnectionFailure();
          return;
        }
        const ip = streamUrl.match(/rtsp:\/\/(.*?):/)?.[1];
        if (!ip) {
          console.error('解析视频流地址失败:', streamUrl);
          handleConnectionFailure();
          return;
        }
        const rtcUrl = webrtcUrl || `http://${ip}:33001`;

        // 清理现有连接和事件监听器
        cleanupVideoConnection();

        // 添加延迟确保资源完全释放
        await new Promise(resolve => setTimeout(resolve, isRetry ? 1000 : 100));

        // 3. 在创建新实例前再次检查
        if (isUnmounted.value) return;

        webRtcServer = new WebRtcStreamer(videoId, rtcUrl);

        // 设置连接超时
        connectionTimeoutTimer = window.setTimeout(() => {
          if (isConnecting.value) {
            console.log('摄像头连接超时');
            isTimeout.value = true;
            handleConnectionFailure();
          }
        }, 15000);

        // 添加视频事件监听器
        const videoElement = document.getElementById(videoId) as HTMLVideoElement;
        if (videoElement) {
          videoEventHandlers.playing = () => {
            if (isUnmounted.value) return;
            console.log('简约版摄像头连接成功');
            isConnecting.value = false;
            connectionFailed.value = false;
            retryCount.value = 0;
            if (connectionTimeoutTimer) clearTimeout(connectionTimeoutTimer);
            connectionTimeoutTimer = null;
            emit('connected');
          };

          videoEventHandlers.error = (event) => {
            if (isUnmounted.value) return;
            console.error('简约版摄像头播放错误:', event);
            handleConnectionFailure();
          };

          videoElement.addEventListener('playing', videoEventHandlers.playing);
          videoElement.addEventListener('error', videoEventHandlers.error);
        }

        // 开始连接
        await webRtcServer.connect(streamUrl);
      } catch (error) {
        // 3. 在捕获到错误时也检查标记
        if (isUnmounted.value) {
          console.log('组件已卸载，忽略连接过程中的错误。');
          return;
        }
        console.error('简约版摄像头连接过程中出错:', error);
        handleConnectionFailure();
      }
    };

    onMounted(() => {
      console.log('SimpleCameraView组件挂载，开始连接摄像头');
      getRtspUrl();
    });

    onUnmounted(() => {
      // 2. 在卸载时设置标记
      isUnmounted.value = true;
      console.log('SimpleCameraView组件卸载，清理资源');

      // 清理所有定时器
      if (retryTimer) {
        clearTimeout(retryTimer);
        retryTimer = null;
      }
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer);
        connectionTimeoutTimer = null;
      }

      // 清理视频连接
      cleanupVideoConnection();

      // 重置状态
      isConnecting.value = false;
      isTimeout.value = false;
      connectionFailed.value = false;
      retryCount.value = 0;
    });

    return {
      videoId,
      isConnecting,
      connectionFailed,
      statusClass,
    };
  },
});
</script>

<style lang="less" scoped>
.simple-camera-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;

  .title {
    margin-bottom: 1.39vw; // 30px / 2160 * 100 = 1.39vw
    font-size: 1.11vw; // 24px / 2160 * 100 = 1.11vw
    font-weight: 500;
    flex-shrink: 0;
    color: #fff;
  }

  .video-container {
    position: relative;
    flex: 1;
    min-height: 0;
    background: #000;
    overflow: hidden;
    border-radius: 0.46vw; // 10px / 2160 * 100 = 0.46vw

    // 确保视频容器能够自适应父容器
    width: 100%;
    height: 100%;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.46vw;
    }

    .status-tag {
      position: absolute;
      top: 1vh;
      right: 1.39vw; // 30px / 2160 * 100 = 1.39vw
      font-size: 0.93vw; // 20px / 2160 * 100 = 0.93vw
      color: #fff;
      z-index: 99;
      width: 4.63vw; // 100px / 2160 * 100 = 4.63vw
      height: 4.63vw;

      &.success {
        background: url('@/assets/images/screen/train-icon-qualified.png') no-repeat center / 100%
          100%;
      }

      &.error {
        background: url('@/assets/images/screen/train-icon-unqualified.png') no-repeat center / 100%
          100%;
      }

      &.warning {
        background: url('@/assets/images/screen/train-icon-warning.png') no-repeat center / 100%
          100%;
      }
    }

    .camera-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.6);
      z-index: 10;
      border-radius: 0.46vw;

      :deep(.ant-spin) {
        color: #fff;

        .ant-spin-text {
          color: #fff;
          font-size: 0.65vw; // 14px / 2160 * 100 = 0.65vw
          margin-top: 0.37vw; // 8px / 2160 * 100 = 0.37vw
        }
      }
    }

    .connection-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10;
      border-radius: 0.46vw;

      .error-icon {
        font-size: 2.22vw; // 48px / 2160 * 100 = 2.22vw
        margin-bottom: 0.74vw; // 16px / 2160 * 100 = 0.74vw
      }

      .error-text {
        color: #fff;
        font-size: 0.74vw; // 16px / 2160 * 100 = 0.74vw
        text-align: center;
      }
    }
  }
}

// 中等屏幕适配
@media screen and (max-width: 1024px) {
  .simple-camera-box {
    .title {
      margin-bottom: 1.2vw;
      font-size: 1vw;
    }

    .video-container {
      .status-tag {
        font-size: 0.8vw;
        width: 4vw;
        height: 4vw;
        right: 1.2vw;
      }

      .camera-loading {
        :deep(.ant-spin) {
          .ant-spin-text {
            font-size: 0.6vw;
          }
        }
      }

      .connection-error {
        .error-icon {
          font-size: 2vw;
          margin-bottom: 0.6vw;
        }

        .error-text {
          font-size: 0.7vw;
        }
      }
    }
  }
}

// 小屏幕适配
@media screen and (max-width: 768px) {
  .simple-camera-box {
    .title {
      margin-bottom: 1vw;
      font-size: 1.2vw;
    }

    .video-container {
      border-radius: 0.8vw;

      video {
        border-radius: 0.8vw;
      }

      .status-tag {
        font-size: 1vw;
        width: 5vw;
        height: 5vw;
        right: 1.5vw;
      }

      .camera-loading {
        border-radius: 0.8vw;

        :deep(.ant-spin) {
          .ant-spin-text {
            font-size: 0.8vw;
          }
        }
      }

      .connection-error {
        border-radius: 0.8vw;

        .error-icon {
          font-size: 2.5vw;
          margin-bottom: 0.8vw;
        }

        .error-text {
          font-size: 0.9vw;
        }
      }
    }
  }
}
</style>
