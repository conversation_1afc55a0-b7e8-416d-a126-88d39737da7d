<template>
  <div class="error-sound-selector">
    <div class="selector-header">
      <h3>错误语音测试选择器</h3>
      <div class="usage-tip">
        <p>💡 使用说明：选择错误类型后，可以播放该类型下的所有错误语音。支持理性和活泼两种声音风格切换。</p>
      </div>
      <div class="controls">
        <a-select
          v-model:value="selectedCategory"
          placeholder="选择错误类型"
          style="width: 200px; margin-right: 16px"
          @change="handleCategoryChange"
        >
          <a-select-option value="all">全部类型</a-select-option>
          <a-select-option value="tightening">拧紧作业</a-select-option>
          <a-select-option value="cap">堵盖检测</a-select-option>
          <a-select-option value="seal">胶条检测</a-select-option>
          <a-select-option value="pipeline">管线检测</a-select-option>
          <a-select-option value="harness">线束插接</a-select-option>
          <a-select-option value="basic">基础检测</a-select-option>
        </a-select>

        <a-select
          v-model:value="soundType"
          style="width: 120px; margin-right: 16px"
          @change="handleSoundTypeChange"
        >
          <a-select-option value="rational">理性语音</a-select-option>
          <a-select-option value="lively">活泼语音</a-select-option>
        </a-select>

        <a-button
          type="primary"
          @click="playAllInCategory"
          :disabled="isPlaying || !currentCategoryErrors.length"
          :loading="isPlaying"
        >
          播放该类型所有错误
        </a-button>

        <a-button
          @click="stopPlayback"
          :disabled="!isPlaying"
          style="margin-left: 8px"
        >
          停止播放
        </a-button>

        <a-button
          @click="testSoundType"
          :disabled="isPlaying"
          style="margin-left: 8px"
        >
          测试当前声音类型
        </a-button>
      </div>
    </div>

    <div class="category-info" v-if="selectedCategory !== 'all'">
      <div class="info-card">
        <h4>{{ getCategoryName(selectedCategory) }}</h4>
        <p>该类型下共有 <strong>{{ currentCategoryErrors.length }}</strong> 个错误</p>
        <div class="error-list">
          <div
            v-for="error in currentCategoryErrors"
            :key="error.type"
            class="error-item"
            :class="{ 'playing': currentPlayingError === error.type }"
          >
            <span class="error-code">{{ error.type }}</span>
            <span class="error-desc">{{ error.description }}</span>
            <span class="error-message">{{ error.errorMessage }}</span>
            <a-button
              size="small"
              @click="playSingleError(error.type)"
              :disabled="isPlaying"
            >
              播放
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <div class="all-categories" v-else>
      <div class="category-grid">
        <div
          v-for="category in categories"
          :key="category.key"
          class="category-card"
          @click="selectCategory(category.key)"
        >
          <h4>{{ category.name }}</h4>
          <p>{{ category.errors.length }} 个错误</p>
          <div class="error-preview">
            <span v-for="error in category.errors.slice(0, 3)" :key="error.type" class="error-tag">
              {{ error.type }}
            </span>
            <span v-if="category.errors.length > 3" class="more-tag">...</span>
          </div>
        </div>
      </div>
    </div>

    <div class="status-bar">
      <div class="status-info">
        <span>播放状态: </span>
        <span :class="{ 'status-playing': isPlaying, 'status-stopped': !isPlaying }">
          {{ isPlaying ? '播放中' : '已停止' }}
        </span>
        <span v-if="isPlaying && currentPlayingError" style="margin-left: 16px">
          当前播放: {{ getErrorDescription(currentPlayingError) }}
        </span>
      </div>
      <div class="progress-info" v-if="isPlaying && playingQueue.length > 1">
        <span>进度: {{ currentPlayingIndex + 1 }} / {{ playingQueue.length }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useErrorSound, type ErrorType, type SoundType } from '@/composables/useErrorSound';

const {
  playErrorSound,
  stopCurrentPlayback,
  switchSoundType,
  getErrorInfo,
  getSoundableErrorTypes,
  ERROR_SOUNDS,
  isPlaying
} = useErrorSound();

const selectedCategory = ref<string>('all');
const soundType = ref<SoundType>('rational');
const currentPlayingError = ref<ErrorType | null>(null);
const currentPlayingIndex = ref(0);
const playingQueue = ref<ErrorType[]>([]);

// 错误分类定义
const categories = computed(() => [
  {
    key: 'tightening',
    name: '拧紧作业',
    errors: [1, 2, 3, 4].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  },
  {
    key: 'cap',
    name: '堵盖检测',
    errors: [21, 22, 23].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  },
  {
    key: 'seal',
    name: '胶条检测',
    errors: [31, 32, 33].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  },
  {
    key: 'pipeline',
    name: '管线检测',
    errors: [41, 42, 43].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  },
  {
    key: 'harness',
    name: '线束插接',
    errors: [51, 52].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  },
  {
    key: 'basic',
    name: '基础检测',
    errors: [8].map(type => ERROR_SOUNDS[type as ErrorType]).filter(e => e.hasSound)
  }
]);

// 当前类别的错误列表
const currentCategoryErrors = computed(() => {
  if (selectedCategory.value === 'all') {
    return getSoundableErrorTypes().map(type => ERROR_SOUNDS[type]);
  }

  const category = categories.value.find(cat => cat.key === selectedCategory.value);
  return category?.errors || [];
});

// 获取类别名称
const getCategoryName = (categoryKey: string) => {
  const category = categories.value.find(cat => cat.key === categoryKey);
  return category?.name || categoryKey;
};

// 获取错误描述
const getErrorDescription = (errorType: ErrorType) => {
  const error = ERROR_SOUNDS[errorType];
  return `${error.description} - ${error.errorMessage}`;
};

// 选择类别
const selectCategory = (categoryKey: string) => {
  selectedCategory.value = categoryKey;
};

// 类别变化处理
const handleCategoryChange = (value: string) => {
  console.log(`切换到类型: ${getCategoryName(value)}`);
  console.log(`该类型下有 ${currentCategoryErrors.value.length} 个错误`);

  // 停止当前播放
  if (isPlaying.value) {
    stopCurrentPlayback();
  }
};

// 声音类型变化处理
const handleSoundTypeChange = (value: SoundType) => {
  console.log(`切换声音类型: ${value === 'rational' ? '理性语音' : '活泼语音'}`);
  switchSoundType(value);
};

// 播放单个错误
const playSingleError = (errorType: ErrorType) => {
  console.log(`播放单个错误: ${getErrorDescription(errorType)}`);
  currentPlayingError.value = errorType;
  playingQueue.value = [errorType];
  currentPlayingIndex.value = 0;

  playErrorSound([{ actionType: errorType }], soundType.value);
};

// 播放该类型所有错误
const playAllInCategory = () => {
  const errors = currentCategoryErrors.value;
  if (errors.length === 0) return;

  const categoryName = getCategoryName(selectedCategory.value);
  console.log(`开始播放 ${categoryName} 类型的所有错误`);
  console.log(`共 ${errors.length} 个错误将依次播放:`);

  // 打印即将播放的错误列表
  errors.forEach((error, index) => {
    console.log(`  ${index + 1}. ${error.type}: ${error.description} - ${error.errorMessage}`);
  });

  const errorActions = errors.map(error => ({ actionType: error.type }));
  playingQueue.value = errors.map(error => error.type);
  currentPlayingIndex.value = 0;
  currentPlayingError.value = errors[0].type;

  // 添加播放完成的回调监听
  const originalIsPlaying = isPlaying.value;
  const checkPlayingStatus = () => {
    if (originalIsPlaying && !isPlaying.value) {
      console.log(`${categoryName} 类型的所有错误播放完成！`);
      return;
    }
    if (isPlaying.value) {
      setTimeout(checkPlayingStatus, 100);
    }
  };

  playErrorSound(errorActions, soundType.value);

  // 开始监听播放状态
  setTimeout(checkPlayingStatus, 100);
};

// 停止播放
const stopPlayback = () => {
  console.log('手动停止播放');
  stopCurrentPlayback();
  currentPlayingError.value = null;
  playingQueue.value = [];
  currentPlayingIndex.value = 0;
};

// 测试当前声音类型
const testSoundType = () => {
  const testErrorType = 8; // 使用手套识别作为测试音频
  const soundTypeName = soundType.value === 'rational' ? '理性语音' : '活泼语音';

  console.log(`测试 ${soundTypeName} - 播放手套识别错误提示音`);

  currentPlayingError.value = testErrorType;
  playingQueue.value = [testErrorType];
  currentPlayingIndex.value = 0;

  playErrorSound([{ actionType: testErrorType }], soundType.value);
};

// 监听播放状态变化
watch(isPlaying, (playing) => {
  if (!playing) {
    currentPlayingError.value = null;
    playingQueue.value = [];
    currentPlayingIndex.value = 0;
  }
});

// 模拟播放进度更新（实际应该从 useErrorSound 获取）
watch(isPlaying, (playing) => {
  if (playing && playingQueue.value.length > 1) {
    // 这里可以添加更精确的播放进度跟踪逻辑
    // 目前简化处理
  }
});
</script>

<style scoped>
.error-sound-selector {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 20px 0;
}

.selector-header {
  margin-bottom: 20px;
}

.selector-header h3 {
  margin: 0 0 12px 0;
  color: #333;
}

.usage-tip {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 16px;
}

.usage-tip p {
  margin: 0;
  color: #0050b3;
  font-size: 14px;
}

.controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.category-info {
  margin-bottom: 20px;
}

.info-card {
  background: white;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
  margin: 0 0 10px 0;
  color: #1890ff;
  font-size: 18px;
}

.info-card p {
  margin: 0 0 16px 0;
  color: #666;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
}

.error-item.playing {
  background: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.error-code {
  font-weight: bold;
  color: #1890ff;
  min-width: 40px;
  margin-right: 12px;
}

.error-desc {
  font-weight: 500;
  color: #333;
  min-width: 120px;
  margin-right: 12px;
}

.error-message {
  color: #666;
  flex: 1;
  margin-right: 12px;
}

.all-categories {
  margin-bottom: 20px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.category-card {
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.category-card h4 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.category-card p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.error-preview {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.error-tag {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #666;
}

.more-tag {
  color: #999;
  font-size: 12px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.status-info {
  display: flex;
  align-items: center;
  color: #666;
}

.status-playing {
  color: #52c41a;
  font-weight: bold;
}

.status-stopped {
  color: #999;
}

.progress-info {
  color: #1890ff;
  font-weight: 500;
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
  }

  .controls > * {
    width: 100%;
    margin-bottom: 8px;
  }

  .category-grid {
    grid-template-columns: 1fr;
  }

  .error-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .status-bar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
