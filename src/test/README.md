# 错误语音播放测试

## 问题描述

当多个错误连续推送时，会出现语音播放重叠的问题。正确的行为应该是：
- 任何新的错误推送都立即停止当前正在播放的语音
- 只播放最新一批错误的完整语音序列
- 确保每个错误的语音都能完整播放，不会有重叠

## 解决方案

在 `useErrorSound.ts` 中实现了防重叠机制：

1. **即时停止**：任何新的错误推送都会立即停止当前播放
2. **队列重建**：使用最新的错误列表重新构建播放队列
3. **顺序播放**：确保每个错误语音都能完整播放
4. **状态管理**：准确跟踪播放状态，避免重叠

## 测试文件说明

### 1. `errorSoundTest.ts`
核心测试逻辑文件，包含：
- 测试数据生成函数
- 重叠测试函数
- 正常播放测试函数
- 单个错误测试函数

### 2. `ErrorSoundTestPanel.vue`
Vue测试组件，提供：
- 可视化测试界面
- 播放状态监控
- 测试日志显示
- 声音类型切换

### 3. `ErrorSoundTest.vue`
完整的测试页面，包含：
- 详细的使用说明
- 技术实现说明
- 响应式布局

### 4. `public/error-sound-test.html`
独立的HTML测试页面，可以直接在浏览器中打开，包含：
- 完整的模拟器实现
- 实时日志显示
- 无需Vue环境即可测试

## 测试数据

### 第一批错误（立即推送）
- 1: 双手持枪 - 未双手作业
- 2: 垂直作业面 - 未垂直作业面
- 3: 拧紧贴合 - 拧紧未贴合

### 第二批错误（500ms后推送）
- 4: 扳手绿灯 - 拧紧枪未亮绿灯
- 8: 手套识别 - 未戴手套
- 21: 堵盖大小判定 - 堵盖选择错误

### 第三批错误（1000ms后推送，最终播放）
- 22: 堵盖贴合判定 - 堵盖不贴合
- 31: 胶条安装判定 - 安装不规范
- 41: 油管距离判定 - 油管未闭合
- 51: 线束插接-手部回拔判定 - 安装完成未回拔

## 使用方法

### 方法1：在Vue项目中使用

1. 导入测试页面组件：
```vue
<template>
  <ErrorSoundTest />
</template>

<script setup>
import ErrorSoundTest from '@/views/ErrorSoundTest.vue'
</script>
```

2. 或者直接使用测试面板组件：
```vue
<template>
  <ErrorSoundTestPanel />
</template>

<script setup>
import ErrorSoundTestPanel from '@/components/ErrorSoundTestPanel.vue'
</script>
```

### 方法2：使用独立HTML页面

直接在浏览器中打开 `public/error-sound-test.html` 文件。

### 方法3：在浏览器控制台中测试

如果项目中已经引入了测试文件，可以在浏览器控制台中直接调用：

```javascript
// 测试快速连续推送
window.testErrorSound.testOverlap()

// 测试播放中被打断
window.testErrorSound.testInterruption()

// 测试正常播放
window.testErrorSound.testNormal()

// 测试单个错误
window.testErrorSound.testSingle()

// 打印错误信息
window.testErrorSound.printInfo()
```

## 预期测试结果

### 快速连续推送测试
- 第一批错误开始播放
- 500ms后第二批错误推送，立即停止第一批播放
- 1000ms后第三批错误推送，立即停止第二批播放
- **最终只播放第三批的4个错误语音**

### 播放中被打断测试
- 第一批错误开始播放（3个错误）
- 播放过程中（约2秒后）推送新错误
- 当前播放立即停止，开始播放新的错误序列
- **新的错误序列完整播放**

### 正常播放测试
- 第一批错误完整播放（3个错误）
- 8秒后第二批错误完整播放（3个错误）
- 16秒后第三批错误完整播放（4个错误）
- **每批错误都能完整播放，无重叠**

### 单个错误测试
- 播放单个"未双手作业"错误语音
- 无队列，直接播放

## 技术要点

1. **即时中断**：任何新错误推送都会立即停止当前播放
2. **播放队列**：使用数组管理多个错误的播放顺序
3. **状态管理**：跟踪播放状态、队列长度、当前索引
4. **错误过滤**：自动过滤无声音的错误类型
5. **音频管理**：预加载音频文件，管理音频元素生命周期
6. **无重叠保证**：确保同一时间只有一个语音在播放

## 调试建议

1. 打开浏览器开发者工具查看控制台日志
2. 观察播放状态的实时变化
3. 注意时间间隔的计算和判断逻辑
4. 检查音频文件是否正确加载
5. 验证错误类型的过滤逻辑

## 注意事项

1. 确保音频文件路径正确
2. 浏览器可能需要用户交互才能播放音频
3. 测试时建议开启音量
4. 某些浏览器可能有自动播放限制
5. 测试环境需要支持Web Audio API
