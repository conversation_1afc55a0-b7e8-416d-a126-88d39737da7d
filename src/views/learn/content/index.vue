<template>
  <div class="learn-content-page">
    <a-page-header title="返回" class="page-header-fixed" @back="goBack">
      <template #extra>
        <div class="study-time-info">
          <span class="study-time-label">学习时长限制：</span>
          <span class="study-time-value">{{ totalTimeLimit }} 分钟</span>
          <span class="study-time-label ml-4">当前学习时长：</span>
          <span class="study-time-value">{{ currentStudyTime }}</span>
        </div>
        <a-button
          @click="debouncePrev"
          :disabled="!hasPrevMaterial || navigationLoading"
          :loading="navigationLoading"
          >上一篇</a-button
        >
        <a-button
          @click="debounceNext"
          :disabled="!hasNextMaterial || navigationLoading"
          :loading="navigationLoading"
          type="primary"
          class="ml-2"
          >下一篇</a-button
        >
      </template>
    </a-page-header>
    <div class="content-container">
      <a-spin :spinning="loading" tip="加载中...">
        <!-- 学习名称和说明区域 -->
        <a-card size="small" v-if="learningInfo" :bordered="false" class="learning-info-card">
          <div class="learning-info-header">
            <h2 class="learning-title">{{ learningInfo.name }}</h2>
            <a-tag
              class="status-tag"
              size="small"
              :color="getStatusColor(learningInfo.status)"
              v-if="learningInfo.status"
              >{{ getStatusText(learningInfo.status) }}</a-tag
            >
          </div>
          <div class="learning-description" v-if="learningInfo.description">
            <div class="description-content" :title="learningInfo.description">{{
              learningInfo.description
            }}</div>
          </div>
        </a-card>

        <a-card size="small" v-if="currentMaterial" :bordered="false" class="content-card">
          <template #title>
            <div class="material-title-wrapper">
              <h2 class="material-title">{{ currentMaterial.filename }}</h2>
              <a-button v-if="paperId && examRecordId" type="primary" @click="handleExam"
                >去考试</a-button
              >
            </div>
          </template>
          <div class="material-meta">
            <span>文件类型：{{ fileType }}</span>
            <span class="ml-4"
              >更新时间：{{
                currentMaterial.lastUpdateTime
                  ? formatToDate(dayjs(currentMaterial.lastUpdateTime))
                  : '-'
              }}</span
            >
          </div>
          <a-divider />

          <template v-if="currentMaterial.fileType && currentMaterial.fileType.includes('video')">
            <div class="video-player-wrapper">
              <VideoPlayer :src="currentMaterial.fileUrl" :options="videoOptions" />
            </div>
          </template>

          <template v-else-if="currentMaterial.fileUrl">
            <div class="document-section">
              <div class="document-preview">
                <FilePreview :fileUrl="currentMaterial.fileUrl" />
              </div>
            </div>
          </template>

          <a-empty v-else description="暂无内容" />
        </a-card>
        <a-empty v-else-if="!loading" description="未找到学习材料" />
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  V1CommonFileUploadDownloadFileIdPost,
  V1ManageUserLearningsLearningDetailId,
  V1ManageUserLearningsIncrementTimePut,
  V1ManageUserLearningsLearningDetailIdStartPut,
  V1ManageUserLearningsOverPost,
} from '/@/api/cddc.req';
import { formatToDate, dateUtil, formatSecondsToMinuteString } from '/@/utils/dateUtil';
import VideoPlayer from '/@/components/VideoPlayer/index.vue';
import FilePreview from '/@/components/FilePreview/index.vue';
import type { MaterialListElement } from '/@/api/cddc.model';
import { message } from '@geega-ui-plus/ant-design-vue';
import { fileTypeOptions } from '../material/material.data';
import { statusColorMap, statusTextMap } from '../home/<USER>';
import { useDebounce } from '/@/hooks/core/useDebounce';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const navigationLoading = ref(false); // 新增：用于控制上一篇/下一篇按钮的加载状态
const currentMaterial = ref<MaterialListElement | null>(null);
const materialList = ref<MaterialListElement[]>([]); // 当前项目下的所有学习材料列表
const currentIndex = ref(-1); // 当前材料在列表中的索引
const learningInfo = ref<{
  name: string;
  description: string;
  status?: string;
}>(); // 学习名称和说明

// 学习时长相关
const totalTimeLimit = ref(0); // 学习时长限制，单位分钟
const elapsedSeconds = ref(0); // 已学习的秒数
const currentStudyTime = computed(() => {
  const hours = Math.floor(elapsedSeconds.value / 3600);
  const minutes = Math.floor((elapsedSeconds.value % 3600) / 60);
  const seconds = elapsedSeconds.value % 60;
  return `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${
    seconds < 10 ? '0' + seconds : seconds
  }`;
});
let timer: number | null = null;
let reportTimer: number | null = null; // 定时上报学习时长的计时器
let studyStartTime: Date | null = null; // 记录学习开始时间

const pageTitle = computed(() => (route.query.name ? `${route.query.name}` : '学习内容'));
const learningDetailId = computed(() => route.query.id as string);
const paperId = ref(''); // 存储试卷ID
const examStatus = ref(''); // 存储考试状态
const examRecordId = ref(''); // 存储考试记录ID

// 文件类型转换
const fileType = computed(() => {
  return (
    fileTypeOptions.find((item) => item.value === currentMaterial.value?.fileType)?.label || '-'
  );
});

const videoOptions = ref({
  autoplay: false,
  controls: true,
  fluid: true,
  // 其他播放器配置
});

// 返回到学习首页
const goBack = async () => {
  // 在返回前调用结束学习接口
  await endLearning();

  router.push({
    path: '/learn/home',
  });
};

const dayjs = dateUtil; // 使用dateUtil作为dayjs的别名

const fetchLearningDetail = async (id: string) => {
  if (!id) return;
  loading.value = true;
  try {
    // 调用API获取学习详情
    const response = await V1ManageUserLearningsLearningDetailId({ learningDetailId: id });
    if (!response) {
      currentMaterial.value = null;
      console.warn('Failed to get learning detail or result is empty.');
      return;
    }

    // 设置学习名称和说明
    learningInfo.value = {
      name: response.name || '未命名学习',
      description: response.description || '',
      status: response.status || '',
    };

    // 获取学习材料列表
    materialList.value = response.materialList || [];

    // 为每个材料获取实际的文件URL
    for (const material of materialList.value) {
      if (material.fileId) {
        try {
          const fileUrl = await V1CommonFileUploadDownloadFileIdPost({
            fileId: material.fileId,
          });

          if (fileUrl) {
            // @ts-ignore 动态添加fileUrl属性
            material.fileUrl = fileUrl;
          }
        } catch (error) {
          console.error(`Failed to fetch file URL for material ${material.id}:`, error);
        }
      }
    }

    // 如果有材料，默认显示第一个
    if (materialList.value.length > 0) {
      currentMaterial.value = materialList.value[0];
      currentIndex.value = 0;
    } else {
      currentMaterial.value = null;
    }

    // 设置项目时长限制和paperId
    if (response.requireDuration) {
      totalTimeLimit.value =
        Number(formatSecondsToMinuteString(Number(response.requireDuration))) || 0;
    }

    // 保存考试paperId
    if (response.paperId) {
      paperId.value = response.paperId;
    }

    // 设置已学习的时长（接口返回的是秒数）
    if (response.duration) {
      elapsedSeconds.value = parseInt(response.duration) || 0;
    }

    // 设置考试状态
    if (response.examStatus) {
      examStatus.value = response.examStatus;
    }

    // 设置考试记录ID
    if (response.examRecordId) {
      examRecordId.value = response.examRecordId;
    }
  } catch (error) {
    console.error('Failed to fetch learning details:', error);
    currentMaterial.value = null;
    materialList.value = [];
  } finally {
    loading.value = false;
  }
};

// 更新学习时长到服务器
const reportLearningTime = async () => {
  if (!learningDetailId.value || !elapsedSeconds.value) return;

  try {
    // 上报累计的总学习时间
    await V1ManageUserLearningsIncrementTimePut({
      id: learningDetailId.value,
      incrementTime: elapsedSeconds.value.toString(),
    });
  } catch (error) {
    console.error('Failed to report learning time:', error);
  }
};

// 去考试按钮处理函数
const handleExam = () => {
  if (examRecordId.value && paperId.value) {
    // 跳转到考试页面并传递examRecordId
    router.push({
      path: '/exam/take',
      query: {
        paperId: paperId.value,
        examRecordId: examRecordId.value,
      },
    });
  } else {
    // 如果没有examRecordId，提示用户
    message.warning('未找到考试记录，请联系管理员');
  }
};

const hasPrevMaterial = computed(() => currentIndex.value > 0);
const hasNextMaterial = computed(
  () => currentIndex.value >= 0 && currentIndex.value < materialList.value.length - 1
);

const navigateToMaterial = (index: number) => {
  if (index >= 0 && index < materialList.value.length) {
    currentIndex.value = index;
    currentMaterial.value = materialList.value[index];
  }
};

/**
 * @description 处理上一篇操作
 */
const handlePrev = () => {
  if (hasPrevMaterial.value) {
    navigationLoading.value = true;
    navigateToMaterial(currentIndex.value - 1);
    // 模拟预览组件加载，2秒后清除loading
    setTimeout(() => {
      navigationLoading.value = false;
    }, 2000);
  }
};

/**
 * @description 处理下一篇操作
 */
const handleNext = () => {
  if (hasNextMaterial.value) {
    navigationLoading.value = true;
    navigateToMaterial(currentIndex.value + 1);
    // 模拟预览组件加载，2秒后清除loading
    setTimeout(() => {
      navigationLoading.value = false;
    }, 2000);
  }
};

// 上一篇和下一篇防抖处理
const [debouncePrev] = useDebounce(handlePrev, 500);
const [debounceNext] = useDebounce(handleNext, 500);

// 开始学习接口调用
const startLearning = async () => {
  if (!learningDetailId.value) return;

  // 记录学习开始时间
  studyStartTime = new Date();

  try {
    await V1ManageUserLearningsLearningDetailIdStartPut({
      learningDetailId: learningDetailId.value,
    });
    console.log('开始学习接口调用成功');
  } catch (error) {
    console.error('开始学习接口调用失败:', error);
  }
};

// 结束学习接口调用
const endLearning = async () => {
  if (!learningDetailId.value) return;

  const endTime = new Date();

  try {
    await V1ManageUserLearningsOverPost({
      id: learningDetailId.value,
      startAt: studyStartTime ? formatToDate(dayjs(studyStartTime), 'YYYY-MM-DD HH:mm:ss') : formatToDate(dayjs(new Date()), 'YYYY-MM-DD HH:mm:ss'),
      endAt: formatToDate(dayjs(endTime), 'YYYY-MM-DD HH:mm:ss'),
    });
    console.log('结束学习接口调用成功');
  } catch (error) {
    console.error('结束学习接口调用失败:', error);
  }
};

// 开始计时
const startStudyTimer = async () => {
  if (timer !== null) return;

  // 调用开始学习接口
  await startLearning();

  // 本地计时，每秒更新显示
  timer = window.setInterval(() => {
    elapsedSeconds.value += 1;
  }, 1000);

  // 服务端上报，每10秒上报一次学习时长
  if (reportTimer === null) {
    reportTimer = window.setInterval(() => {
      reportLearningTime();
    }, 10 * 1000); // 10秒
  }
};

// 停止计时
const stopStudyTimer = async () => {
  // 停止本地计时器
  if (timer !== null) {
    window.clearInterval(timer);
    timer = null;
  }

  // 停止服务端上报计时器
  if (reportTimer !== null) {
    window.clearInterval(reportTimer);
    reportTimer = null;
  }

  // 最后一次上报学习时长
  await reportLearningTime();
};

watch(
  learningDetailId,
  (newId) => {
    if (newId) {
      fetchLearningDetail(newId as string);
    }
  },
  { immediate: true }
);

// 监听当前学习材料的变化，有内容才开始计时，无内容则停止
watch(
  currentMaterial,
  (newMaterial) => {
    if (newMaterial && totalTimeLimit.value > 0) {
      startStudyTimer();
    } else {
      stopStudyTimer();
    }
  },
  { immediate: true }
);

// 组件卸载时停止计时并结束学习
onUnmounted(async () => {
  await stopStudyTimer();
  // 如果有开始时间，说明已经开始学习，需要调用结束学习接口
  if (studyStartTime) {
    await endLearning();
  }
});

// 获取状态对应的中文文本
const getStatusText = (status: string) => {
  return statusTextMap[status] || status;
};

// 获取状态对应的颜色
const getStatusColor = (status: string) => {
  return statusColorMap[status] || 'default';
};
</script>

<style lang="less" scoped>
.learn-content-page {
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  height: 100%;
}

.page-header-fixed {
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 24px; /* 减小顶部内边距 */

  .study-time-info {
    display: inline-flex;
    align-items: center;
    margin-right: 24px;

    .study-time-label {
      color: rgba(0, 0, 0, 0.65);
    }

    .study-time-value {
      font-weight: 500;
      color: #00996b;
      margin-left: 4px;

      &:first-of-type {
        color: #52c41a;
      }
    }
  }
  :deep(.cddc-ant-page-header-heading-title) {
    line-height: inherit;
  }
}

.content-container {
  flex: 1; /* 使内容区域占据剩余所有空间 */
  display: flex;
  flex-direction: column;
  padding: 12px; /* 减小内边距 */
  overflow-y: auto;

  /* 使卡片填充内容区域 */
  :deep(.ant-spin-container),
  :deep(.ant-card) {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  :deep(.ant-card-body) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.content-card {
  width: 100%;

  .material-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .material-title {
    font-size: 20px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 0;
    flex: 1;
  }
  .material-meta {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 18px;
    color: #1bae56;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .video-player-wrapper {
    background-color: #000;
    width: 100%;
    /* 限制最大高度，确保播放控件在可视区域内 */
    max-height: 60vh;
    aspect-ratio: 16 / 9;
    border-radius: 4px;
    overflow: hidden;
    position: relative;

    :deep(.video-player-container) {
      /* 确保视频内容适应容器 */
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      max-height: 50vh;
      aspect-ratio: 16 / 9;

      @media (min-aspect-ratio: 16/9) {
        height: 50vh;
        width: calc(50vh * 16 / 9);
      }
    }
  }

  .document-section {
    // margin-top: 24px;
  }

  .document-preview {
    max-width: 100%;
    background-color: #f9f9f9;
    border-radius: 8px;
    height: 600px; /* 设置预览区域高度 */

    :deep(iframe) {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  .article-content {
    line-height: 1.8;
    font-size: 16px;
    color: #333;
    word-wrap: break-word;
    flex: 1; /* 文章内容占据剩余空间 */

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    :deep(p) {
      margin-bottom: 16px;
    }
    :deep(img) {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 16px auto;
      border-radius: 4px;
    }
    :deep(ul),
    :deep(ol) {
      padding-left: 20px;
      margin-bottom: 16px;
    }
    :deep(li) {
      margin-bottom: 8px;
    }
    :deep(blockquote) {
      border-left: 4px solid #e8e8e8;
      padding-left: 16px;
      margin: 16px 0;
      color: rgba(0, 0, 0, 0.65);
    }
    :deep(pre) {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      overflow-x: auto;
      margin: 16px 0;
    }
    :deep(code) {
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 2px;
      font-size: 0.9em;
    }
    :deep(pre code) {
      background-color: transparent;
      padding: 0;
      font-size: 1em;
    }
  }
}

.ml-2 {
  margin-left: 8px;
}
.ml-4 {
  margin-left: 16px;
}

.learning-info-card {
  margin-bottom: 12px;

  .learning-info-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .learning-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      margin: 0;
    }
    .status-tag {
      padding: 4px 6px;
    }
  }

  .learning-description {
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 8px;

    .description-title {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 8px;
    }

    .description-content {
      color: rgba(0, 0, 0, 0.65);
      line-height: 1.6;
      white-space: pre-line;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
