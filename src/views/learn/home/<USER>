<template>
  <div class="learn-index-wrapper">
    <BasicTablePlus @register="registerTable">
      <!-- 考试分数 -->
      <template #form-scoreRange="{ model, field }">
        <a-input-group compact>
          <a-input-number
            v-model:value="(model[field] || [])[0]"
            placeholder="最低分"
            :min="0"
            style="width: 45%"
            @change="(value) => handleScoreRangeChange(0, value, model, field)"
          />
          <a-input class="score-number" placeholder="~" disabled />
          <a-input-number
            v-model:value="(model[field] || [])[1]"
            placeholder="最高分"
            :min="(model[field] || [])[0]"
            class="max-score"
            @change="(value) => handleScoreRangeChange(1, value, model, field)"
          />
        </a-input-group>
      </template>
      <!-- 学习时长 -->
      <template #form-durationRange="{ model, field }">
        <a-input-group compact>
          <a-input-number
            v-model:value="(model[field] || [])[0]"
            placeholder="最短时长"
            :min="0"
            :precision="0"
            style="width: 45%"
            @change="(value) => handleScoreRangeChange(0, value, model, field)"
          />
          <a-input class="score-number" placeholder="~" disabled />
          <a-input-number
            v-model:value="(model[field] || [])[1]"
            placeholder="最长时长"
            :min="(model[field] || [])[0]"
            :precision="0"
            class="max-score"
            @change="(value) => handleScoreRangeChange(1, value, model, field)"
          />
        </a-input-group>
      </template>

      <!-- 操作列自定义内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <!-- examStatus -->
        <template v-if="column.dataIndex === 'examStatus'">
          <a-tag :color="getStatusColor(record.examStatus)" v-if="record.examStatus">
            {{ getStatusText(record.examStatus) }}
          </a-tag>
          <span v-else>-</span>
        </template>
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a
              class="action-button"
              @click="handleStartLearn(record)"
              v-if="record.status == 'WAITING'"
            >
              开始学习
            </a>
            <a
              class="action-button"
              @click="handleContinueLearn(record)"
              v-if="record.status == 'LEARNING'"
            >
              继续学习
            </a>
            <a class="action-button" @click="handleReview(record)" v-if="record.status == 'FINISH'">
              复习
            </a>
          </a-space>
        </template>
      </template>
    </BasicTablePlus>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import BasicTablePlus from '/@/components/BasicTablePlus/index.vue';
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { useRouter } from 'vue-router';
import { Tag } from '@geega-ui-plus/ant-design-vue';
import { columns, statusColorMap, statusTextMap } from './home.data';
import {
  V1ManageUserLearningsLearningDetailIdStartPut,
  V1ManageUserLearningsPagePost,
} from '/@/api/cddc.req';
import { createLocalStorage } from '/@/utils/cache';
export default defineComponent({
  name: 'LearnIndex',
  components: {
    BasicTablePlus,
    ATag: Tag,
  },
  setup() {
    const router = useRouter();

    // 处理分数范围变化
    const handleScoreRangeChange = (
      index: number,
      value: number | null,
      model: any,
      field: string
    ) => {
      // 确保model[field]是一个数组
      if (!model[field]) {
        model[field] = [null, null];
      }
      // 如果第一个值大于第二个值，将第二个值设置为第一个值
      if (value !== null && model[field][0] > model[field][1]) {
        model[field][1] = model[field][0];
      }
      // 更新数组中的值
      model[field][index] = value;
    };
    // 处理请求参数
    const handleBeforeFetch = (params: any) => {
      const rawQuery = params.data || {};

      // 处理分数范围
      let minScore, maxScore;
      if (rawQuery.scoreRange && Array.isArray(rawQuery.scoreRange)) {
        [minScore, maxScore] = rawQuery.scoreRange;
      }

      // 处理学习时长范围
      let minDuration, maxDuration;
      if (rawQuery.durationRange && Array.isArray(rawQuery.durationRange)) {
        [minDuration, maxDuration] = rawQuery.durationRange;
      }
      const ls = createLocalStorage();
      const userId = ls.get('userId');
      const data = {
        ...rawQuery,
        userId: userId || '',
        startTime: rawQuery.timeRange?.at(0),
        endTime: rawQuery.timeRange?.at(1),
        minScore, // 最低分数
        maxScore, // 最高分数
        minDuration, // 最短学习时长
        maxDuration, // 最长学习时长
      };

      // 删除原始的scoreRange和durationRange参数，避免传递给后端
      if (data.scoreRange) {
        delete data.scoreRange;
      }

      if (data.durationRange) {
        delete data.durationRange;
      }

      params.data = data;
      return params;
    };

    // 表格注册
    const [registerTable, { reload }] = useTable({
      api: (params) => V1ManageUserLearningsPagePost(handleBeforeFetch(params)),
      columns,
      tableProps: {
        showIndexColumn: false,
      },
      beforeFetch: handleBeforeFetch,
    });

    // 获取状态颜色
    const getStatusColor = (status: string) => {
      return statusColorMap[status] || 'default';
    };

    // 获取状态文本
    const getStatusText = (status: string) => {
      return statusTextMap[status] || '-';
    };

    // 开始学习
    const handleStartLearn = async (record: Recordable) => {
      // 跳转到学习内容页面
      router.push({
        path: `/learn/content`,
        query: { id: record.id, name: record.name },
      });
    };

    // 继续学习
    const handleContinueLearn = (record: Recordable) => {
      // 跳转到学习内容页面，继续上次的学习进度
      router.push({
        path: `/learn/content`,
        query: { id: record.id, name: record.name, continue: 'true' },
      });
    };

    // 复习
    const handleReview = (record: Recordable) => {
      // 跳转到学习内容页面，标记为复习模式
      router.push({
        path: `/learn/content`,
        query: { id: record.id, name: record.name, review: 'true' },
      });
    };

    return {
      registerTable,
      handleStartLearn,
      handleContinueLearn,
      handleReview,
      getStatusColor,
      getStatusText,
      handleScoreRangeChange,
    };
  },
});
</script>

<style lang="less" scoped>
.learn-index-wrapper {
  padding: 0px 8px;
}
.score-number {
  width: 10%;
  border-left: 0;
  border-right: 0;
  pointer-events: none;
  text-align: center;
  height: 25px;
}
.max-score {
  width: 45%;
  border-left: 0;
}
</style>
