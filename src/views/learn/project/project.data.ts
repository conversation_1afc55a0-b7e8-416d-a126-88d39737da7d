import type { FormSchema } from '@geega-ui-plus/geega-ui';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { V1ManageExamPaperPagePost, V1ManageLearningMaterialsPagePost } from '/@/api/cddc.req';
import { formatSecondsToMinuteString } from '/@/utils/dateUtil';

// 表格列定义
export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'name',
    width: 180,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入项目名称',
      },
    },
  },
  {
    title: '时长限制(分钟)',
    dataIndex: 'duration',
    width: 120,
    search: {
      component: 'Input',
      componentProps: {
        type: 'number',
        min: 1,
        max: 999,
        placeholder: '请输入时长限制',
      },
    },
    customRender: ({ text }) => {
      return formatSecondsToMinuteString(Number(text));
    },
  },
  {
    title: '学习文件',
    dataIndex: 'materialList',
    width: 200,
    customRender: ({ text }) => {
      if (Array.isArray(text)) {
        return text.map((item) => item.filename).join(', ');
      }
      return text;
    },
  },
  {
    title: '关联考试',
    dataIndex: 'paperName',
    width: 150,
  },
  {
    title: '变更时间',
    dataIndex: 'lastUpdateTime',
    width: 180,
    search: {
      field: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
      componentProps: {
        allowClear: true,
        showTime: { format: 'HH:mm:ss' },
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        style: { width: '100%' },
      },
    },
  },
  {
    title: '变更人员',
    dataIndex: 'lastUpdateBy',
    width: 120,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入变更人员',
      },
    },
  },
];

// 表单配置（新增/编辑）
export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '项目名称',
    component: 'Input',
    required: true,
    rules: [{ required: true, message: '请输入项目名称' }],
  },
  {
    field: 'duration',
    label: '时长限制(分钟)',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 999,
      style: { width: '100%' },
    },
    rules: [{ required: true, message: '请输入时长限制' }],
  },
  {
    field: 'materialIdList',
    label: '学习文件',
    component: 'ApiSelect',
    componentProps: {
      api: async () => {
        const { records } = await V1ManageLearningMaterialsPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: {},
        });
        return records?.map((o) => ({ label: o.filename, value: o.id }));
      },
      showSearch: true,
      showArrow: true,
      optionFilterProp: 'label',
      mode: 'multiple',
      maxTagCount: 'responsive',
      maxCount: 5,
    },
    rules: [
      { required: true, type: 'array', message: '请选择学习文件', trigger: 'change' },
      { type: 'array', max: 5, message: '最多只能选择5个文件', trigger: 'blur' },
    ],
  },
  {
    field: 'paperId',
    label: '关联考试',
    component: 'ApiSelect',
    componentProps: {
      api: async () => {
        const { records } = await V1ManageExamPaperPagePost({
          pageSize: 1000,
          currentPage: 1,
          data: { questionPropertyList: [], status: 'RELEASE', type: 'SKILL_INSPECTION' },
        });
        return records?.map((o) => ({ label: o.name, value: o.id }));
      },
      optionFilterProp: 'label',
      showSearch: true,
      showArrow: true,
    },
    rules: [{ required: false, message: '请选择关联考试' }],
  },
  {
    field: 'description',
    label: '学习说明',
    component: 'InputTextArea',
    componentProps: {
      rows: 6,
      maxlength: 10000,
      placeholder: '请输入学习说明信息',
    },
  },
];

// 模拟数据
export const mockData = [
  {
    id: '1',
    projectName: '装配基础',
    timeLimit: 60,
    learningFile: '装配作业指导书.docx',
    relatedExam: '装配基础考试',
    updateTime: '2024-06-01 10:00',
    updater: '张三',
  },
  {
    id: '2',
    projectName: '安全培训',
    timeLimit: 45,
    learningFile: '安全培训PPT.pptx',
    relatedExam: '安全培训考核',
    updateTime: '2024-06-02 09:30',
    updater: '李四',
  },
  {
    id: '3',
    projectName: '焊接工艺',
    timeLimit: 90,
    learningFile: '焊接工艺流程.pdf',
    relatedExam: '焊接工艺测试',
    updateTime: '2024-06-03 14:20',
    updater: '王五',
  },
];
