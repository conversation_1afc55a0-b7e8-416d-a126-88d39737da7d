<template>
  <div class="error-sound-demo">
    <div class="demo-header">
      <h1>错误语音测试演示</h1>
      <p class="demo-description">
        这个页面演示了错误语音系统的功能。你可以选择不同的错误类型，测试理性和活泼两种声音风格，
        并查看每个类型下有多少个错误，然后依次播放所有错误的语音。
      </p>
    </div>
    
    <ErrorSoundSelector />
    
    <div class="demo-features">
      <h2>功能特点</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <h3>🎵 双声音风格</h3>
          <p>支持理性和活泼两种声音风格，满足不同用户偏好</p>
        </div>
        <div class="feature-card">
          <h3>📂 分类管理</h3>
          <p>按照作业类型分类管理错误，包括拧紧、堵盖、胶条、管线、线束等</p>
        </div>
        <div class="feature-card">
          <h3>🔄 智能播放</h3>
          <p>支持单个错误播放和批量播放，新错误会自动打断当前播放</p>
        </div>
        <div class="feature-card">
          <h3>📊 实时状态</h3>
          <p>实时显示播放状态、当前播放的错误和播放进度</p>
        </div>
        <div class="feature-card">
          <h3>🎯 精确控制</h3>
          <p>支持手动停止播放，快速测试声音类型</p>
        </div>
        <div class="feature-card">
          <h3>📱 响应式设计</h3>
          <p>适配不同屏幕尺寸，在手机和平板上也能正常使用</p>
        </div>
      </div>
    </div>
    
    <div class="demo-usage">
      <h2>使用方法</h2>
      <div class="usage-steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>选择错误类型</h4>
            <p>从下拉菜单中选择要测试的错误类型，或选择"全部类型"查看所有分类</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>选择声音风格</h4>
            <p>在理性语音和活泼语音之间切换，可以点击"测试当前声音类型"听到区别</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>播放错误语音</h4>
            <p>点击"播放该类型所有错误"依次播放，或点击单个错误的"播放"按钮</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>查看播放状态</h4>
            <p>底部状态栏会显示当前播放状态、正在播放的错误和播放进度</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="demo-tips">
      <h2>提示</h2>
      <ul>
        <li>🔊 确保浏览器允许音频播放，某些浏览器需要用户交互后才能播放音频</li>
        <li>⏸️ 播放过程中推送新的错误会立即停止当前播放，开始播放新的错误序列</li>
        <li>🎧 建议佩戴耳机或调整音量到合适大小</li>
        <li>📝 播放过程中会在浏览器控制台输出详细的日志信息</li>
        <li>🔄 切换声音类型时会重新初始化音频元素，可能有短暂延迟</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import ErrorSoundSelector from '@/components/ErrorSoundSelector.vue';
</script>

<style scoped>
.error-sound-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.demo-header h1 {
  margin: 0 0 16px 0;
  font-size: 2.5em;
  font-weight: 300;
}

.demo-description {
  font-size: 1.1em;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.demo-features {
  margin: 40px 0;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 12px;
}

.demo-features h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-card h3 {
  margin: 0 0 12px 0;
  color: #1890ff;
  font-size: 1.2em;
}

.feature-card p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.demo-usage {
  margin: 40px 0;
  padding: 30px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
}

.demo-usage h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.usage-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1em;
}

.step-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.demo-tips {
  margin: 40px 0;
  padding: 30px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
}

.demo-tips h2 {
  margin: 0 0 20px 0;
  color: #856404;
}

.demo-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #856404;
}

.demo-tips li {
  margin-bottom: 8px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .error-sound-demo {
    padding: 15px;
  }
  
  .demo-header {
    padding: 20px;
  }
  
  .demo-header h1 {
    font-size: 2em;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-features, .demo-usage, .demo-tips {
    padding: 20px;
  }
  
  .step {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    align-self: center;
  }
}
</style>
