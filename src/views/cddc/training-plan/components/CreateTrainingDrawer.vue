<script setup lang="ts">
import { reactive } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import dayjs from 'dayjs';
import { FORM_SCHEMAS, FORM_CONFIG, type Training } from '../data';
import { V1ManageTrainBookingsPost, V1ManageTrainBookingsPut } from '/@/api/cddc.req';
import { useDebounce } from '/@/hooks/core/useDebounce';

const emit = defineEmits(['success', 'register']);

const state = reactive({
  drawerTitle: '新建训练计划',
  isEditMode: false,
  currentTrainingId: '',
});

const [registerDrawer, drawerActions] = useDrawerInner(
  async (data?: { selectedDate?: any } & Partial<Training>) => {
    state.isEditMode = !!(data && data.id);
    state.drawerTitle = state.isEditMode ? '编辑训练计划' : '新建训练计划';
    state.currentTrainingId = data?.id || '';

    // 根据编辑模式动态更新表单配置
    const schemas = FORM_SCHEMAS.createTraining.map((schema) => {
      if (schema.field === 'date') {
        // 根据模式设置日期字段的禁用状态
        return {
          ...schema,
          componentProps: {
            ...schema.componentProps,
            disabled: state.isEditMode, // 编辑模式禁用，新建模式启用
          },
        };
      }
      return schema;
    });

    // 更新表单配置
    await formActions.updateSchema(schemas);
    await formActions.resetFields();
    if (state.isEditMode && data) {
      // 编辑模式，设置表单值
      await formActions.setFieldsValue({
        timeRange: data.timeRange,
        classId: data.classId,
        instructorId: data.instructorId,
        date: data.date,
        content: data.content,
      });
    } else if (data?.selectedDate) {
      // 新建模式，设置默认日期
      await formActions.setFieldsValue({
        date: data.selectedDate,
      });
    } else {
      // 新建模式，没有传入日期时设置为当前日期
      await formActions.setFieldsValue({
        date: dayjs(),
      });
    }
  }
);

const [registerForm, formActions] = useForm({
  schemas: FORM_SCHEMAS.createTraining,
  showActionButtonGroup: false,
  labelWidth: 100,
  baseColProps: { span: 24 },
  rowProps: { gutter: [0, 16] },
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });
  const values = await formActions.validate();
  try {
    const [startTime, endTime] = values.timeRange;

    // 验证：只能预约从当前日期开始的后6天内的日期
    const today = dayjs();
    const maxDate = today.add(6, 'day'); // 当前日期后6天

    if (values.date.isAfter(maxDate, 'day')) {
      message.error('只能预约从当前日期开始的后6天内的日期');
      drawerActions.setDrawerProps({
        visible: true,
        confirmLoading: false,
      });
      return;
    }

    // 验证：不能预约已经过去的日期
    if (values.date.isBefore(today, 'day')) {
      message.error('不能预约已经过去的日期');
      drawerActions.setDrawerProps({
        visible: true,
        confirmLoading: false,
      });
      return;
    }

    // 验证：如果是当天时间，不能选择当前时间之前的时间
    if (values.date.isSame(dayjs(), 'day')) {
      const now = dayjs();
      const startDateTime = values.date.hour(startTime.hour()).minute(startTime.minute());

      if (startDateTime.isBefore(now)) {
        message.error('当天预约时间不能早于当前时间');
        drawerActions.setDrawerProps({
          visible: true,
          confirmLoading: false,
        });
        return;
      }
    }

    // 将日期和时间组合成完整的时间戳
    const startDateTime = values.date
      .hour(startTime.hour())
      .minute(startTime.minute())
      .second(0)
      .millisecond(0);

    const endDateTime = values.date
      .hour(endTime.hour())
      .minute(endTime.minute())
      .second(0)
      .millisecond(0);

    // 根据模式调用不同的API
    if (state.isEditMode) {
      // 编辑模式：调用更新API
      await V1ManageTrainBookingsPut({
        id: state.currentTrainingId,
        startTime: startDateTime.format('YYYY-MM-DD HH:mm:ss'), // 转换为日期字符串格式
        endTime: endDateTime.format('YYYY-MM-DD HH:mm:ss'), // 转换为日期字符串格式
        unitId: values.classId, // 工位ID
        userId: values.instructorId, // 培训员工ID
      });
    } else {
      // 创建模式：调用创建API
      await V1ManageTrainBookingsPost({
        startTime: startDateTime.format('YYYY-MM-DD HH:mm:ss'), // 转换为日期字符串格式
        endTime: endDateTime.format('YYYY-MM-DD HH:mm:ss'), // 转换为日期字符串格式
        unitId: values.classId, // 工位ID
        userId: values.instructorId, // 培训员工ID
      });
    }
    message.success(state.isEditMode ? '编辑成功' : FORM_CONFIG.messages.createSuccess);
    emit('success', 1);

    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });
    console.error(state.isEditMode ? '编辑训练预约失败:' : '创建训练预约失败:', error);
    message.error(state.isEditMode ? '编辑失败，请重试' : '创建失败，请重试');
  }
}

// 创建防抖版本的提交函数
const [debounceSubmit] = useDebounce(handleSubmit, 500);
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="debounceSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
