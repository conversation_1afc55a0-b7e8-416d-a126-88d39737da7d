<template>
  <div class="training-plan-container">
    <!-- Header -->
    <div class="header">
      <div class="date-selectors">
        <a-select v-model:value="selectedYear" class="year-selector">
          <a-select-option v-for="year in years" :key="year" :value="year">
            {{ year }}年
          </a-select-option>
        </a-select>
        <a-select v-model:value="selectedMonth" class="month-selector">
          <a-select-option v-for="month in months" :key="month.value" :value="month.value">
            {{ month.label }}
          </a-select-option>
        </a-select>
      </div>
      <a-button type="primary" class="create-btn" @click="handleCreateTraining"> 新建 </a-button>
    </div>

    <!-- Calendar -->
    <div class="calendar">
      <!-- Week Header -->
      <div class="week-header">
        <div v-for="day in weekDays" :key="day" class="week-day">
          {{ day }}
        </div>
      </div>

      <!-- Calendar Body -->
      <div class="calendar-body">
        <div v-for="week in calendarWeeks" :key="week.weekIndex" class="calendar-week">
          <div
            v-for="day in week.days"
            :key="day.date"
            :class="[
              'calendar-day',
              {
                'other-month': day.isOtherMonth,
                today: day.isToday,
                'has-data': day.allTrainings.length > 0,
              },
            ]"
            @click="selectDay(day)"
          >
            <div class="day-number">
              <label class="num">{{ day.dayNumber }}</label>
            </div>
            <div class="day-content">
              <div v-for="(training, index) in day.trainings" :key="index" class="training-item">
                <span
                  class="training-dot"
                  :class="getTrainingStatusClass(training.status || 'WAITING')"
                ></span>
                <span class="training-text">{{ training.text }}</span>
              </div>
              <div v-if="day.moreCount > 0" class="more-items" @click.stop="showMoreTrainings(day)">
                还有{{ day.moreCount }}个项目
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Training Drawer -->
    <CreateTrainingDrawer @register="registerCreateDrawer" @success="onTrainingCreated" />

    <!-- Training Details Modal -->
    <BasicModal
      @register="registerDetailsModal"
      :title="`${selectedDay?.dayNumber}日训练安排`"
      width="1000px"
      :height="420"
      :show-ok-btn="false"
      :show-cancel-btn="false"

    >
      <BasicTable
        v-if="selectedDay"
        :columns="detailColumns"
        :dataSource="selectedDay.allTrainings"
        :pagination="false"
        size="small"
        :maxHeight="400"
        canResize
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <div class="status-dot" :style="{ backgroundColor: getStatusConfig(record.status || 'WAITING').color }"></div>
          </template>
          <template v-if="column.dataIndex === 'timeRange'">
            {{ record.timeRange[0].format('HH:mm') }}-{{ record.timeRange[1].format('HH:mm') }}
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="flex gap-2">
              <a-button
                type="text"
                size="small"
                :disabled="isTrainingExpired(record)"
                @click="editTraining(record)"
              >
                编辑
              </a-button>
              <a-button
                type="text"
                size="small"
                danger
                :disabled="isTrainingExpired(record)"
                @click="deleteTraining(record)"
              >
                删除
              </a-button>
            </div>
          </template>
        </template>
      </BasicTable>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { BasicModal, useModal, useDrawer, BasicTable } from '@geega-ui-plus/geega-ui';
import dayjs from 'dayjs';
import CreateTrainingDrawer from './components/CreateTrainingDrawer.vue';
import {
  type Training,
  type CalendarDay,
  type CalendarWeek,
  WEEK_DAYS,
  MONTHS,
  CALENDAR_CONFIG,
  FORM_CONFIG,
  getYearOptions,
  getTrainingStatusClass,
  generateMockTrainings,
  detailColumns,
  getStatusConfig,
} from './data';
import {
  V1ManageTrainBookingsBookingIdDelete,
  V1ManageTrainBookingsListPost,
} from '/@/api/cddc.req';

// State
const selectedYear = ref(dayjs().year());
const selectedMonth = ref(dayjs().month() + 1);
const selectedDay = ref<CalendarDay | null>(null);

// Modal state
const [registerDetailsModal, detailsModalActions] = useModal();

// Drawer state
const [registerCreateDrawer, { openDrawer: openCreateDrawer }] = useDrawer();

// Mock training data
const trainings = ref<Training[]>([]);

// Constants
const weekDays = WEEK_DAYS;
const years = computed(() => getYearOptions());
const months = MONTHS;

// Computed
const calendarWeeks = computed(() => {
  const weeks: CalendarWeek[] = [];
  const firstDay = dayjs(`${selectedYear.value}-${selectedMonth.value}-01`);
  const lastDay = firstDay.endOf('month');
  const startDate = firstDay.startOf('week');
  const endDate = lastDay.endOf('week');

  let currentDate = startDate;
  let weekIndex = 0;

  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    const week: CalendarWeek = {
      weekIndex: weekIndex++,
      days: [],
    };

    for (let i = 0; i < 7; i++) {
      const dayTrainings = trainings.value.filter((training) =>
        training.date.isSame(currentDate, 'day')
      );

      const displayTrainings = dayTrainings.slice(0, CALENDAR_CONFIG.maxDisplayItems);
      const moreCount = Math.max(0, dayTrainings.length - CALENDAR_CONFIG.maxDisplayItems);

      const day: CalendarDay = {
        date: currentDate.format('YYYY-MM-DD'),
        dayNumber: currentDate.date(),
        isOtherMonth: !currentDate.isSame(firstDay, 'month'),
        isToday: currentDate.isSame(dayjs(), 'day'),
        trainings: displayTrainings,
        allTrainings: dayTrainings,
        moreCount,
      };

      week.days.push(day);
      currentDate = currentDate.add(1, 'day');
    }

    weeks.push(week);
  }

  return weeks;
});



// Methods
const selectDay = (day: CalendarDay) => {
  if (day.allTrainings.length > 0) {
    selectedDay.value = day;
    detailsModalActions.openModal();
  }
};

const showMoreTrainings = (day: CalendarDay) => {
  selectedDay.value = day;
  detailsModalActions.openModal();
};



const handleCreateTraining = () => {
  // 传递当前日期作为默认值
  const selectedDate = dayjs();
  openCreateDrawer(true, { selectedDate });
};

const editTraining = (training: Training) => {
  // 关闭当日训练安排弹框
  detailsModalActions.openModal(false);
  // 打开编辑抽屉，传递训练数据
  openCreateDrawer(true, training);
};

const deleteTraining = async (training: Training) => {
  try {
    await V1ManageTrainBookingsBookingIdDelete({ bookingId: training.id });

    // 从主要的 trainings 数组中删除
    const mainIndex = trainings.value.findIndex((t) => t.id === training.id);
    if (mainIndex > -1) {
      trainings.value.splice(mainIndex, 1);
    }

    // 如果当前有选中的日期，更新其 allTrainings
    if (selectedDay.value) {
      const dayIndex = selectedDay.value.allTrainings.findIndex((t) => t.id === training.id);
      if (dayIndex > -1) {
        selectedDay.value.allTrainings.splice(dayIndex, 1);
      }

      // 重新计算当天的训练数据以保持一致性
      const dayTrainings = trainings.value.filter((t) =>
        t.date.isSame(dayjs(selectedDay.value!.date), 'day')
      );
      selectedDay.value.allTrainings = dayTrainings;
      selectedDay.value.trainings = dayTrainings.slice(0, CALENDAR_CONFIG.maxDisplayItems);
      selectedDay.value.moreCount = Math.max(
        0,
        dayTrainings.length - CALENDAR_CONFIG.maxDisplayItems
      );
    }

    message.success(FORM_CONFIG.messages.deleteSuccess);
  } catch (error) {
    console.error('删除训练计划失败:', error);
    message.error('删除失败，请重试');
  }
};

// 判断实训计划是否已过期（结束时间早于当前时间）
const isTrainingExpired = (training: Training): boolean => {
  const now = dayjs();
  const endTime = training.timeRange[1];
  const trainingDate = training.date;

  // 构建完整的结束时间（日期 + 时间）
  const fullEndTime = trainingDate
    .hour(endTime.hour())
    .minute(endTime.minute())
    .second(endTime.second());

  return fullEndTime.isBefore(now);
};

// Initialize training data
const initTrainingData = async () => {
  try {
    // 计算当月的开始和结束时间
    const startOfMonth = dayjs(`${selectedYear.value}-${selectedMonth.value}-01`).startOf('month');
    const endOfMonth = dayjs(`${selectedYear.value}-${selectedMonth.value}-01`).endOf('month');

    // 调用API获取训练预约列表
    const response = await V1ManageTrainBookingsListPost({
      startTime: startOfMonth.format('YYYY-MM-DD HH:mm:ss'), // 使用日期时间字符串格式
      endTime: endOfMonth.format('YYYY-MM-DD HH:mm:ss'), // 使用日期时间字符串格式
    });

    if (response && Array.isArray(response)) {
      // 转换API响应数据为Training格式
      trainings.value = response.map((booking: any) => {
        const startTime = dayjs(booking.startTime);
        const endTime = dayjs(booking.endTime);

        return {
          id: booking.id || Date.now().toString(),
          text: `${startTime.format('HH:mm')}-${endTime.format('HH:mm')} ${booking.unitName || '未知工位'} ${booking.userName || '未知用户'}`,
          color: '',
          content: booking.content || '',
          timeRange: [startTime, endTime],
          className: booking.unitName || '未知工位',
          classId: booking.unitId || '',
          bookingAt: booking.bookingAt || '未知时间',
          bookingBy: booking.bookingBy || '未知用户',
          instructorId: booking.userId || '',
          instructor: booking.userName || '未知用户',
          date: startTime,
          status: booking.status || 'WAITING', // 直接使用API状态
          participants: booking.participants || 0,
        } as Training;
      });
    } else {
      trainings.value = [];
    }
  } catch (error) {
    console.error('获取训练计划失败:', error);
    message.error('获取训练计划失败，显示模拟数据');
    // 如果API调用失败，使用模拟数据
    trainings.value = generateMockTrainings();
  }
};

const onTrainingCreated = () => {
  // 重新加载数据以获取最新的训练计划
  initTrainingData();
};

// 监听年月变化，重新加载数据
watch([selectedYear, selectedMonth], () => {
  initTrainingData();
});

onMounted(() => {
  initTrainingData();
});
</script>

<style lang="less" scoped>
.training-plan-container {
  --ignore-color: #1e1e1e;
  background-color: var(--ignore-color);
  color: #ffffff;
  height: calc(100vh - 90px);
  display: flex;
  flex-direction: column;
  padding: 14px 8px;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    .date-selectors {
      display: flex;
      gap: 16px;

      .year-selector,
      .month-selector {
        min-width: 100px;

        :deep(.cddc-ant-select-selector) {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: #ffffff;

          .cddc-ant-select-selection-item {
            color: #ffffff;
          }
        }

        :deep(.cddc-ant-select-arrow) {
          color: #ffffff;
        }
      }
    }

    .create-btn {
      width: 84px;
    }
  }

  .calendar {
    background: rgba(255, 255, 255, 0.05);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    --ignore-color: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--ignore-color);

    .week-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: var(--ignore-color);
      flex-shrink: 0;

      .week-day {
        padding: 5px 16px;
        text-align: center;
        font-weight: 500;
        border-bottom: 1px solid var(--ignore-color);

        &:last-child {
          border-right: none;
        }
      }
    }

    .calendar-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
      overflow-y: auto;

      .calendar-week {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        border-bottom: 1px solid var(--ignore-color);
        flex: 1;

        &:last-child {
          border-bottom: none;
        }
      }

      .calendar-day {
        height: 100%;
        padding: 8px;
        border-right: 1px solid var(--ignore-color);
        transition: background-color 0.3s;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        &.has-data {
          cursor: pointer;
        }

        &:last-child {
          border-right: none;
        }

        &.has-data:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        &.other-month {
          opacity: 0.5;
        }

        &.today {
          // background: rgba(24, 144, 255, 0.1);
          .day-number {
            .num {
              border-radius: 4px;
              padding: 0 8px;
              background: var(--Brand-Brand7-Normal, #00996b);
            }
          }
        }

        .day-number {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 5px;
          flex-shrink: 0;
          text-align: center;
        }

        .day-content {
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .training-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
            flex-shrink: 0;

            .training-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 4px;
              flex-shrink: 0;

              // 已完成状态 - 绿色渐变
              &.completed {
                background: linear-gradient(180deg, #00996b 0%, rgba(0, 153, 107, 0.3) 100%);
              }

              // 进行中状态 - 蓝色渐变
              &.ongoing {
                background: linear-gradient(180deg, #4080ff 0%, rgba(64, 128, 255, 0.3) 100%);
              }

              // 已预定未发生状态 - 灰色渐变
              &.scheduled {
                background: linear-gradient(180deg, #999 0%, rgba(153, 153, 153, 0.3) 100%);
              }

              // 已过期未参加状态 - 红色渐变
              &.expired {
                background: linear-gradient(180deg, #ec544d 0%, rgba(236, 84, 77, 0.3) 100%);
              }
            }

            .training-text {
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .more-items {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            flex-shrink: 0;
            text-align: center;

            &:hover {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

// Modal overrides for dark theme
:deep(.cddc-ant-modal) {
  .cddc-ant-modal-content {
    background: #1a1f2e;
    color: #ffffff;
  }

  .cddc-ant-modal-header {
    background: #1a1f2e;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .cddc-ant-modal-title {
      color: #ffffff;
    }
  }

  .cddc-ant-modal-close {
    color: #ffffff;
  }

  .cddc-ant-modal-body {
    padding: 16px;
    max-height: calc(500px - 120px); // 弹框高度减去header和footer的高度
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

// Table styles in modal
:deep(.cddc-ant-table) {
  background: transparent;
  color: #ffffff;

  .cddc-ant-table-thead > tr > th {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .cddc-ant-table-tbody > tr > td {
    background: transparent;
    color: #ffffff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .cddc-ant-table-tbody > tr:hover > td {
    background: rgba(255, 255, 255, 0.05);
  }

  .cddc-ant-table-container {
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .cddc-ant-table-body {
    max-height: 350px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .cddc-ant-table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .cddc-ant-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .cddc-ant-table-container {
    flex: 1;
    overflow: hidden;
  }
}

:deep(.cddc-ant-form-item-label > label) {
  color: #ffffff;
}

:deep(.cddc-ant-input),
:deep(.cddc-ant-picker),
:deep(.cddc-ant-time-picker) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

:deep(.cddc-ant-picker-input input) {
  color: #ffffff;
}

:deep(.cddc-ant-input:focus),
:deep(.cddc-ant-picker:focus),
:deep(.cddc-ant-time-picker:focus) {
  border-color: #00b96b;
  box-shadow: 0 0 0 2px rgba(0, 185, 107, 0.2);
}

// 禁用按钮样式
:deep(.cddc-ant-btn[disabled]) {
  color: #666666 !important;
  background-color: transparent !important;
  border-color: transparent !important;
  cursor: not-allowed;
  opacity: 1;

  &:hover {
    color: #666666 !important;
    background-color: transparent !important;
    border-color: transparent !important;
    opacity: 1;
  }

  &:focus {
    color: #666666 !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }
}

// 禁用的危险按钮（删除按钮）样式
:deep(.cddc-ant-btn[disabled].cddc-ant-btn-dangerous) {
  color: #666666 !important;
  background-color: transparent !important;
  border-color: transparent !important;

  &:hover {
    color: #666666 !important;
    background-color: transparent !important;
    border-color: transparent !important;
  }

  &:focus {
    color: #666666 !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }
}

// 状态圆点样式
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto;
}
</style>
