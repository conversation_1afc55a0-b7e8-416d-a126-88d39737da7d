<template>
  <div class="assessment-container">
    <!-- 顶部信息 -->
    <div class="header">
      <div class="assessment-select">
        <a-button class="float-back-btn" type="link" @click="goBack">
          <SvgIcon name="g-ic-line-left" />
          <span class="back-text">返回</span>
        </a-button>
        <ProjectSelector
          title="考核项目"
          @change="handleProjectChange"
          :showTimeInfo="true"
          :remaining-time="formattedTime"
          :is-time-warning="isLastMinute"
          :validOperations="realTimeData.validOperations"
          operations-label="剩余作业次数"
        />
        <!-- <a-button type="primary" @click="pushTest">考核数据测试</a-button> -->
      </div>
    </div>
    <!-- 摄像头画面 -->
    <div class="camera-views">
      <CameraView
        :show-status-tag="true"
        :status-result="realTimeData?.operationLog?.result"
        @connected="handleCameraConnected"
        @disconnected="handleCameraDisconnected"
        @error="handleCameraError"
      />
      <div class="line"></div>
      <TeachingView :point-data="pointData" :diagram-type="projectDiagramType" />
    </div>
    <!-- 考核实时数据 -->
    <div class="flex gap-5 assessment-data">
      <div class="section-grid">
        <OperationLog
          ref="operationLogRef"
          class="flex-1 w-0"
          :detailId="assessmentId"
          :data="realTimeData"
          style="flex: 3"
        />
        <LiveData class="flex-1 w-0" :data="realTimeData" style="flex: 2" />
        <AssessmentProgress
          class="w-81"
          :data="assessmentProgress"
          :formattedTime="formattedTime"
          :isLastMinute="isLastMinute"
          @timeUp="handleTimeUp"
          style="flex: 1"
        />
      </div>
    </div>
    <!-- 底部信息 -->
    <div class="footer">
      <div class="action-buttons">
        <a-button
          type="primary"
          @click="debounceStartAssessment"
          v-if="!isStart"
          :disabled="!isPlayerConnected"
          :loading="loading"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          {{ isPlayerConnected ? '开始考核' : '等待摄像头连接...' }}
        </a-button>
        <template v-if="isStart">
          <a-button
            type="primary"
            @click="submitAssessment"
            v-if="assessmentStatus === AssessmentStatusEnum.IN_PROGRESS"
          >
            <template #icon>
              <CheckCircleOutlined />
            </template>
            提交考核</a-button
          >
          <a-button @click="exitAssessment">
            <template #icon>
              <CloseCircleOutlined />
            </template>
            退出考核</a-button
          >
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from '@geega-ui-plus/ant-design-vue';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@geega-ui-plus/icons-vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';
import { debounce } from 'lodash-es';
import type { AssessmentResponse, RealTimeData } from './types';
import { AssessmentStatusEnum } from './types';
import { useWebSocketHandler } from '/@/composables/useWebSocketHandler';
import ProjectSelector from '/@/components/MyProject/ProjectSelector.vue';
import CameraView from '/@/components/CameraView/index.vue';
import TeachingView from '/@/components/TeachingView/index.vue';
import type { DiagramType } from '/@/components/TeachingView/index.vue';
import type { ProjectInfo } from '/@/types/project';
import OperationLog from '../training/components/OperationLog.vue';
import LiveData from './components/LiveData.vue';
import AssessmentProgress from './components/AssessmentProgress.vue';
import { useGlobSetting } from '/@/hooks/setting';
import { createLocalStorage } from '/@/utils/cache';
import {
  V1LocationStudyExitPost,
  V1LocationStudyInitPost,
  V1LocationStudyOverPost,
  V1LocationStudyStartPost,
  V1LocationStudyVideoCreatePost,
  V1LocationStudyVideoStopPost,
} from '/@/api/cddc.req';
import { useCountdown } from '/@/composables/useCountdown';
import { useOperationLogErrorSound } from '/@/composables/useSmartErrorSound';
import { ProjectType } from '/@/enums/projectEnum';

// State
const router = useRouter();
const route = useRoute();
const isStart = ref(false);
const loading = ref(false);
const isPlayerConnected = ref(false);
const assessmentId = ref('');
const assessmentStatus = ref(AssessmentStatusEnum.NOT_STARTED);
const operationLogRef = ref();
const currentProjectId = ref('');
const realTimeData = ref<RealTimeData>({
  overallScore: 0,
  validOperations: 0,
  invalidOperations: 0,
  failedOperations: 0,
  operationLog: undefined,
});
const pointData = ref([]);
const currProjectName = ref('');
const projectDiagramType = ref<DiagramType>();
const assessmentProgress = ref({
  recordId: '',
  requestDuration: 0,
  requestFrequency: 0,
  requestQualificationRate: 0,
  requestActionRate: 0,
});

// 获取全局配置
const { socketUrl } = useGlobSetting();

// 使用倒计时 composable
const { isLastMinute, formattedTime, start, reset } = useCountdown({
  onTimeUp: handleTimeUp,
  autoStart: true,
});

// 音频处理
const { handleOperationLogError } = useOperationLogErrorSound();

// WebSocket handlers
const handleAssessmentDataUpdate = (data: AssessmentResponse) => {
  const currentValidOperations = assessmentProgress.value.requestFrequency;

  realTimeData.value = {
    ...realTimeData.value,
    ...data,
    validOperations: data.effectiveNum
      ? Math.max(0, currentValidOperations - data.effectiveNum)
      : currentValidOperations,
  };

  // 处理音频播报
  if (data.operationLog) {
    handleOperationLogError(data.operationLog, 'rational');
  }
};

const exitAssessment = () => {
  const ls = createLocalStorage();
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出考核吗？退出后当前考核进度将丢失。',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      // 提交考核
      exitPost();
      assessmentStatus.value = AssessmentStatusEnum.NOT_STARTED;
      router.back();
    },
  });
};

const { initWebSocket, cleanupWebSocket } = useWebSocketHandler({
  type: 'assessment',
  onDataUpdate: handleAssessmentDataUpdate,
  onExit: exitAssessment,
  onPointUpdate: (points) => {
    pointData.value = points;
  },
  messages: {
    exitConfirm: true,
    exitTitle: '确认退出',
    exitContent: '考核正在进行中，确定要退出吗？',
    overMessage: '检测到人员已离开作业，自动结束本次考核',
  },
});

// Event handlers
const handleProjectChange = async (project: ProjectInfo) => {
  const projectId = route.query.projectId as string;
  const projectName = project.name as string;
  if (!projectId || !projectName) {
    message.error('项目信息不完整');
    return;
  }

  try {
    loading.value = true;
    const ls = createLocalStorage();
    currentProjectId.value = projectId;
    currProjectName.value = projectName;
    // 设置图示类型
    projectDiagramType.value = (project.countAlgorithm as DiagramType) || 'tighten';

    // 如果正在考核中，先结束当前考核
    if (assessmentStatus.value === AssessmentStatusEnum.IN_PROGRESS) {
      cleanupWebSocket();
      isStart.value = false;
    }

    // 创建视频流
    await V1LocationStudyVideoCreatePost({
      projectId,
      userId: ls.get('userId'),
      locationId: ls.get('locationId'),
    });
  } catch (error) {
    message.error('切换项目失败，请重试');
  } finally {
    loading.value = false;
  }
};

// const pushTest = () => {
//   realTimeData.value = testData;
// };

// Camera event handlers
const handleCameraConnected = () => {
  isPlayerConnected.value = true;
};

const handleCameraDisconnected = () => {
  isPlayerConnected.value = false;
};

const handleCameraError = () => {
  isPlayerConnected.value = false;
};

// Actions
const debounceStartAssessment = debounce(async () => {
  if (loading.value) return;
  if (!isPlayerConnected.value) {
    message.warning('请等待摄像头连接成功后再开始考核');
    return;
  }
  loading.value = true;
  const ls = createLocalStorage();
  try {
    const res = await V1LocationStudyInitPost({
      projectId: currentProjectId.value,
      userId: ls.get('userId'),
    });

    if (!res?.id) {
      throw new Error('获取考核ID失败');
    }

    assessmentId.value = res.id;
    // 设置初始剩余操作次数和考核时长
    assessmentProgress.value = {
      ...assessmentProgress.value,
      ...res,
    };
    realTimeData.value.validOperations = res.requestFrequency || 0;

    // 初始化WebSocket连接
    if (!socketUrl) {
      throw new Error('缺少WebSocket连接地址');
    }

    initWebSocket({
      socketUrl: socketUrl,
      detailId: res.id,
    });

    const locationId = ls.get('locationId');
    await V1LocationStudyStartPost({ detailId: res.id, locationId });

    isStart.value = true;
    assessmentStatus.value = AssessmentStatusEnum.IN_PROGRESS;
  } catch (error) {
    message.error('开始考核失败');
  } finally {
    loading.value = false;
  }
}, 300);

const submitAssessment = async () => {
  const ls = createLocalStorage();
  try {
    // 提交考核
    await V1LocationStudyOverPost({
      locationId: ls.get('locationId'),
      detailId: assessmentId.value,
    });
    assessmentStatus.value = AssessmentStatusEnum.COMPLETED;
    isStart.value = false;
    cleanupWebSocket();
    // 跳转到报告页面
    router.push({
      path: '/cddc/report',
      query: {
        recordId: assessmentProgress.value?.recordId,
        origin: 'train',
        type: ProjectType.Exam,
      },
    });
  } catch (error) {
    message.error('提交考核失败');
  }
};

const goBack = () => {
  if (isStart.value) {
    Modal.confirm({
      title: '确认退出',
      content: '考核正在进行中，确定要退出吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        exitPost();
        router.back();
      },
    });
    return;
  }
  router.back();
};

// 处理倒计时结束
async function handleTimeUp() {
  message.warning('考核时间已到！');
  const ls = createLocalStorage();
  if (assessmentStatus.value === AssessmentStatusEnum.IN_PROGRESS) {
    try {
      // 调用提交考核接口
      await V1LocationStudyOverPost({
        locationId: ls.get('locationId'),
        detailId: assessmentId.value,
      });
      assessmentStatus.value = AssessmentStatusEnum.COMPLETED;
      isStart.value = false;
      cleanupWebSocket();
      // 跳转到报告页面
      router.push({
        path: '/cddc/report',
        query: {
          recordId: assessmentProgress.value?.recordId,
          origin: 'train',
          type: ProjectType.Exam,
        },
      });
    } catch (error) {
      message.error('提交考核失败');
    }
  }
}

// 监听目标时长变化
watch(
  () => assessmentProgress.value?.requestDuration,
  (newDuration) => {
    if (newDuration && newDuration > 0) {
      // 将分钟转换为秒
      const durationInSeconds = newDuration * 60;
      start(durationInSeconds);
    } else {
      reset();
    }
  },
  { immediate: true }
);

// 退出考核
const exitPost = async () => {
  const ls = createLocalStorage();
  await V1LocationStudyExitPost({
    locationId: ls.get('locationId'),
    detailId: assessmentId.value,
  });
  isStart.value = false;
  cleanupWebSocket();
};

onUnmounted(async () => {
  const ls = createLocalStorage();
  if (isStart.value) {
    // 退出考核
    exitPost();
  }

  // 关闭视频流
  if (currentProjectId.value) {
    await V1LocationStudyVideoStopPost({
      projectId: currentProjectId.value,
      userId: ls.get('userId'),
      locationId: ls.get('locationId'),
    });
  }
});
</script>

<style lang="less" scoped>
.assessment-container {
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  color: #ffffff;
  background: #040405 url('/@/assets/images/screen/page-bg.png') no-repeat center / 100% 100%;
  padding: 1.5vh 2vw;

  .header {
    margin-bottom: 1.5vh;
    flex-shrink: 0;

    .assessment-select {
      margin-bottom: 1.5vh;
      position: relative;
      display: flex;
      align-items: center;
      .float-back-btn {
        width: auto;
        color: #fff;
        height: auto;
        font-size: 16px;
        .back-text {
          margin-left: 4px;
          &::after {
            content: '';
            display: inline-block;
            width: 2px;
            height: 12px;
            --ignore-dark-bg: rgba(255, 255, 255, 0.2);
            background: var(--ignore-dark-bg);
            margin-left: 12px;
          }
        }
      }
    }
  }

  .camera-views {
    display: flex;
    gap: 0.6vw;
    margin-bottom: 1.5vh;
    flex: 1;
    min-height: 0;
    background: url('/@/assets/images/screen/train-camera-bg.png') no-repeat center / 100% 100%;
    padding: 1vh 1.5vw 3vh 1.4vw;
    position: relative;

    .line {
      width: 0.2vw;
      --ignore-dark-img: url('/@/assets/images/screen/train-line.png');
      background: var(--ignore-dark-img) no-repeat center / 100% 100%;
      margin: 9vh 0 3vh 0;
    }

    .btn-switch {
      position: absolute;
      right: 2vw;
      top: 3vh;
    }

    .camera-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      border-radius: 4px;

      .title {
        margin-bottom: 2vh;
        font-size: 1.1vw;
        font-weight: 500;
        flex-shrink: 0;
      }

      .video-container {
        position: relative;
        aspect-ratio: 16/9;
        flex: 1;
        min-height: 0;
        background: #000;
        overflow: hidden;
        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        #camera2 {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          :deep(video) {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }

        .status-tag {
          position: absolute;
          top: 1vh;
          right: 1vw;
          font-size: 0.9vw;
          color: #fff;
          z-index: 99;
          width: 5.6vw;
          height: 5.6vw;

          &.success {
            background: url('@/assets/images/screen/train-icon-qualified.png') no-repeat center /
              100% 100%;
          }

          &.error {
            background: url('@/assets/images/screen/train-icon-unqualified.png') no-repeat center /
              100% 100%;
          }
        }

        .camera-loading {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.6);
          z-index: 10;

          :deep(.ant-spin) {
            color: #fff;

            .ant-spin-text {
              color: #fff;
              font-size: 14px;
              margin-top: 8px;
            }
          }
        }
      }
    }

    .point-box {
      flex: 1;
      padding: 5vh 0 0.5vh 0;
    }
  }

  .assessment-data {
    flex-shrink: 0;
    min-height: 300px;

    .section-grid {
      display: flex;
      gap: 1vw;
      width: 100%;
      height: 100%;
    }
  }

  .footer {
    display: flex;
    width: 56vw;
    height: 6vh;
    margin: 0 auto;
    flex-shrink: 0;
    background: url('/@/assets/images/screen/train-btn-bg.png') no-repeat center / 100% 100%;
    justify-content: center;
    &.assessment {
      padding-left: 7.3vw;
      justify-content: flex-start;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1.1vw;

      .cddc-ant-btn {
        min-width: 12.2vw;
        height: 5.2vh;
        border-radius: 4px;
        font-size: clamp(14px, 1.8vw, 40px);
        font-weight: 500;
      }
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .assessment-container {
    padding: 1.2vh 1.6vw;

    .header {
      margin-bottom: 1.2vh;

      .assessment-select {
        margin-bottom: 1.2vh;
      }
    }

    .camera-views {
      gap: 0.5vw;
      margin-bottom: 1.2vh;
      padding: 0.8vh 1.2vw 2.5vh 1.2vw;

      .line {
        margin: 8vh 0 2.5vh 0;
      }

      .btn-switch {
        right: 1.6vw;
        top: 2.5vh;
      }

      .camera-box {
        .title {
          margin-bottom: 1.6vh;
          font-size: 1vw;
        }

        .video-container {
          .status-tag {
            font-size: 0.8vw;
            width: 5vw;
            height: 5vw;
          }
        }
      }

      .point-box {
        padding: 4vh 0 0.4vh 0;
      }
    }

    .assessment-data {
      margin-bottom: 1.2vh;
      height: 35vh;
      min-height: 280px;

      .section-grid {
        gap: 0.8vw;
      }
    }

    .footer {
      width: 60vw;
      height: 5vh;

      .action-buttons {
        gap: 1vw;

        .cddc-ant-btn {
          min-width: 7vw;
          height: 3.5vh;
          font-size: 1vw;
        }
      }
    }
  }
}
</style>
