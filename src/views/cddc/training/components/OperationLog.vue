<template>
  <div class="operation-log" :class="simple?'simple':''">
    <ScreenTitle>操作日志</ScreenTitle>
    <ScreenBox class="flex-1">
      <div class="log-list box-content">
        <template v-if="logList?.length">
          <div class="log-row single-log" v-for="(item, rowIndex) in logList" :key="`log-${item.operationTime || item.id || rowIndex}`">
            <div class="log-item">
              <div class="log-icon" :class="getStatusClass(item.result)"></div>
              <div class="log-content">
                <div class="log-time">{{
                  dayjs(item.operationTime).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
                <div class="log-action" v-if="getActionText(item)">{{ getActionText(item) }}</div>
                <div class="log-tag-box">
                  <div
                    :class="['log-tag', getStatusClass(it.color)]"
                    v-for="(it, index) in item.actionLabels"
                    :key="`tag-${it.desc || it.id || index}`"
                    >{{ it.desc }}</div
                  >
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 空数据 -->
        <GeegaEmpty v-else description="暂无数据" />
      </div>
    </ScreenBox>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import dayjs from 'dayjs';
import { V1LocationStudyOperationLogPagePost } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { watchImmediate } from '@vueuse/core';
import { createLocalStorage } from '/@/utils/cache';


const props = defineProps<{
  detailId?: string;
  data?: any;
  simple?:boolean;
}>();



// 本地维护的日志列表
const localLogList = ref<any[]>([]);

// 操作日志
const apiData = useAsyncData(
  async () => {
    if (Object.keys(props.data).length || !props.detailId) return { records: [] };
    const ls = createLocalStorage();
    const locationId = ls.get('locationId');
    if (!locationId) return { records: [] };

    const result = await V1LocationStudyOperationLogPagePost({
      pageSize: 10,
      currentPage: 1,
      data: {
        detailId: props.detailId,
        locationId: locationId,
      },
    });
    // 初始化本地日志列表
    localLogList.value = result.records || [];
    return result;
  },
  { records: [] }
);

watchImmediate(
  () => props.detailId,
  () => apiData.load()
);

// 清空本地日志
const clearLocalLog = () => {
  localLogList.value = [];
};

const logList = computed(() => {
  if (Object.keys(props.data).length && props.data.operationLog) {
    const newLog = props.data.operationLog;
    // 检查新日志是否已存在
    if (!localLogList.value.some((item) => item.id === newLog.id)) {
      localLogList.value = [newLog, ...localLogList.value];
    }
  }
  return localLogList.value;
});

const getStatusClass = (result: number) => {
  return {
    success: result === 1,
    error: result === 0,
    warning: result === 2,
  };
};

const getActionText = (item: any) => {
  // 只有合格的操作才显示"完成一次操作"
  // 不合格或不达标的操作不显示文本，避免与右侧标签重复
  if (item.result === 1) {
    return '完成一次操作';
  }

  // 不合格或不达标时返回空字符串，让右侧的错误标签来显示具体信息
  return '';
};



defineExpose({
  clearLocalLog,
});
</script>

<style lang="less" scoped>
@import './style/operation-log.less';
</style>
