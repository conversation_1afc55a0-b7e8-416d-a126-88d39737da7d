<script lang="ts" setup>
import { computed } from 'vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import type { V1LocationHomeProgressStatisticsRecordIDGetResponseResult } from '/@/api/cddc.model';

const props = defineProps<{
  /**
   * 报表数据
   */
  data?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
  loading?: boolean;
}>();

const statsData = computed(() => {
  const data = props.data;
  return [
    {
      label: '动作达标率',
      value: Number(data?.totalActionRate || 0),
      unit: '%',
      color: '#29E8AB',
    },
    {
      label: '作业合格率',
      value: Number(data?.totalQualificationRate || 0),
      unit: '%',
      color: '#29E8AB',
    },
  ];
});
const percentageData = computed(() => {
  const data = props.data || {};
  return {
    op: Number(data.totalActionRate || 0),
    all: Number(data.totalQualificationRate || 0),
  };
});
</script>

<template>
  <div class="simple-training-stats">
    <ScreenTitle>训练总计</ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="props.loading">
      <ScreenBox class="stats-container h-full">
        <div class="stats-grid">
          <div v-for="(item, index) in statsData" :key="index" class="stats-item">
            <div class="stats-up">
              <div class="stats-icon">
                <img v-if="index === 0" src="@/assets/images/screen/stats-icon-1.png" alt="" />
                <img v-else src="@/assets/images/screen/stats-icon-2.png" alt="" />
              </div>
              <div class="stats-content">
                <div class="stats-value" :style="{ color: item.color }">
                  {{ item.value }}
                  <span class="stats-unit">{{ item.unit }}</span>
                </div>
                <div class="stats-label">{{ item.label }}</div>
              </div>
            </div>
            <div class="percentage-bar">
              <div class="percentage" :style="{ width: `${index === 0 ? percentageData.op : percentageData.all}%` }"></div>
            </div>
          </div>
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.simple-training-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-container {
  padding: 20px;
}

.stats-grid {
  display: flex;
  gap: 0.93vw;
  height: 100%;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  --ignore-bg-color: rgba(62, 71, 81, 0.2);
  background-color: var(--ignore-bg-color);
  width: 100%;

  .stats-up {
    display: flex;
    align-items: center;
    padding: 1.29vw 1.1vw;
    gap: 0.74vw;
    --ignore-dark-image: url('@/assets/svg/screen/bg-rate.svg');
    background: var(--ignore-dark-image) no-repeat right top / auto 100%;
    width: 100%;
  }

  .stats-icon {
    width: 3.68vw;
    height: 3.68vw;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .stats-content {
    flex: 1;

    .stats-value {
      font-size: 1.29vw;
      font-weight: bold;
      line-height: 1;
      margin-bottom:0.41vw;

      .stats-unit {
        font-size: 0.55vw;
        --ignore-color:rgba(255, 255, 255, 0.60);
        color:var(--ignore-color);
        margin-left: 2px;
      }
    }

    .stats-label {
      font-size: 0.83vw;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.2;
    }
  }
  .percentage-bar {
    width: 100%;
    height: 0.7vw;
    display: flex;
    --ignore-dark-color: #252a2f;
    background-color: var(--ignore-dark-color);
    overflow: hidden;
    position: relative;

    .percentage {
      height: 100%;
      --ignore-dark-image: url('@/assets/svg/screen/bg-percent.png');
      background: var(--ignore-dark-image) repeat left top / auto 100%;
      width: 50%;
    }
  }
}

@media screen and (max-width: 1024px) {
  .stats-grid {
    gap: 12px;
  }

  .stats-item {
    padding: 12px;
    gap: 8px;

    .stats-icon {
      width: 32px;
      height: 32px;
    }

    .stats-content {
      .stats-value {
        font-size: 24px;

        .stats-unit {
          font-size: 14px;
        }
      }

      .stats-label {
        font-size: 12px;
      }
    }
  }
}
</style>
