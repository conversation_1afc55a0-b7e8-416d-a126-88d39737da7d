<template>
  <div class="simple-training-content">
    <!-- 顶部进度区域 -->
    <div class="top-progress-area">
      <div class="progress-section">
        <SimpleProgressReport :data="trainProgress" />
      </div>
      <div class="stats-section">
        <SimpleTrainingStats :data="realTimeData" />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-area">
      <!-- 左侧摄像头画面 -->
      <div class="camera-section">
        <SimpleCameraView
          :show-status-tag="true"
          :status-result="realTimeData?.operationLog?.result"
          @connected="handleCameraConnected"
          @disconnected="handleCameraDisconnected"
          @error="handleCameraError"
        />
        <!-- 训练视频按钮 -->
        <div class="training-video-btn">
          <a-button @click="openTrainingVideo"> 视频教程 </a-button>
        </div>
      </div>

      <!-- 右侧操作日志区域 -->
      <div class="operation-section">
        <!-- <EncouragementBar :data="trainProgress" /> -->
        <SimpleOperationLog ref="operationLogRef" :detailId="trainId" :data="realTimeData" />
      </div>
    </div>

    <!-- 视频学习弹框 -->
    <BasicModal
      @register="register"
      title="训练视频"
      :minHeight="400"
      width="42vw"
      centered
      destroyOnClose
      :showCancelBtn="false"
      :maskClosable="false"
      ok-text="关闭"
      @ok="openModal(false)"
    >
      <VideoPlayer v-if="videoUrl" :src="videoUrl" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SimpleCameraView from '/@/components/CameraView/SimpleCameraView.vue';
import SimpleOperationLog from './SimpleOperationLog.vue';
import SimpleProgressReport from './SimpleProgressReport.vue';
import SimpleTrainingStats from './SimpleTrainingStats.vue';
import type { RealTimeData, TrainProgress } from '../data';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import VideoPlayer from '/@/components/VideoPlayer/index.vue';
import { V1ManageProcessAlgorithmWithCode } from '/@/api/cddc.req';
import { useGlobSetting } from '/@/hooks/setting';
import { message } from '@geega-ui-plus/ant-design-vue';
import type { DiagramType } from '/@/components/TeachingView/index.vue';

// Props 定义
interface Props {
  trainId: string;
  realTimeData: RealTimeData;
  trainProgress: TrainProgress;
  projectDiagramType?: DiagramType;
}

const props = defineProps<Props>();

// Emits 定义
interface Emits {
  (e: 'camera-connected'): void;
  (e: 'camera-disconnected'): void;
  (e: 'camera-error'): void;
}

const emit = defineEmits<Emits>();

// Refs
const operationLogRef = ref<any>(null);

// 训练视频相关
const { trainingUrl } = useGlobSetting();
const [register, { openModal }] = useModal();
const videoUrl = ref('');

// 摄像头事件处理
const handleCameraConnected = () => {
  emit('camera-connected');
};

const handleCameraDisconnected = () => {
  emit('camera-disconnected');
};

const handleCameraError = () => {
  emit('camera-error');
};

// 打开训练视频
const openTrainingVideo = async () => {
  try {
    const videoInfo = await V1ManageProcessAlgorithmWithCode({
      code: props.projectDiagramType,
    });
    videoUrl.value = videoInfo?.videoUrl || trainingUrl || '/example.mp4';
    openModal();
  } catch (error) {
    message.error('获取视频信息失败，请重试');
  }
};

// 暴露方法给父组件
const clearOperationLog = () => {
  operationLogRef.value?.clearLocalLog();
};

defineExpose({
  clearOperationLog,
});
</script>

<style lang="less" scoped>
.simple-training-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  gap: 1.5vh;
  padding-bottom: 1.25vh;

  .top-progress-area {
    display: flex;
    gap: 1vw;
    flex-shrink: 0;

    .progress-section {
      flex: 1;
    }

    .stats-section {
      flex: 1;
    }
  }

  .main-content-area {
    display: flex;
    gap: 0.92vw;
    flex: 1;
    min-height: 0;

    .camera-section {
      min-height: 0;
      flex: 0 0 62.22vw; // 使用flex-basis替代固定width，保持原有比例
      max-width: 62.22vw; // 添加最大宽度限制
      background: url('@/assets/images/screen/train-simple-camera-bg.png') no-repeat center / 100%
        100%;
      padding: 1vh 1.5vw 3vh 1.4vw;
      position: relative;

      .training-video-btn {
        position: absolute;
        right: 2.5vw;
        top: 1.5vw;
        display: flex;
        gap: 8px;
        z-index: 10;

        .cddc-ant-btn {
          font-size:0.83vw;
          padding: 4px 12px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .operation-section {
      min-height: 0;
      flex: 1;
      min-width: 0; // 防止内容溢出
      padding-top: 0.92vw;
      display: flex;
      flex-direction: column;
    }
  }
}

@media screen and (max-width: 1024px) {
  .simple-training-content {
    gap: 1.2vh;

    .top-progress-area {
      height: 120px;
      gap: 0.8vw;
    }

    .main-content-area {
      gap: 0.8vw;

      .camera-section {
        flex: 0 0 60vw; // 小屏幕下调整摄像头区域比例
        max-width: 60vw;
        padding: 0.8vh 1.2vw 2.5vh 1.2vw;

        .training-video-btn {
          top: 0.8vh;
          right: 1.2vw;

          .cddc-ant-btn {
            font-size: 0.8vw;
            padding: 3px 10px;
          }
        }
      }

      .operation-section {
        flex: 1;
        min-width: 0;
        max-width: calc(40vw - 0.8vw); // 确保右侧区域不会过宽
      }
    }
  }
}

// 超小屏幕适配 (MacBook Pro 13寸等)
@media screen and (max-width: 768px) {
  .simple-training-content {
    .main-content-area {
      flex-direction: column; // 小屏幕下改为垂直布局
      gap: 1vh;

      .camera-section {
        flex: none;
        width: 100%;
        max-width: none;
        aspect-ratio: 16/9; // 保持摄像头区域宽高比

        .training-video-btn {
          top: 0.8vh;
          right: 1vw;

          .cddc-ant-btn {
            font-size: 12px;
            padding: 2px 8px;
          }
        }
      }

      .operation-section {
        flex: 1;
        max-width: none;
        padding-top: 0;
      }
    }
  }
}
</style>
