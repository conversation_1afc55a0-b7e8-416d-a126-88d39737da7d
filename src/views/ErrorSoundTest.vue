<template>
  <div class="error-sound-test-page">
    <div class="page-header">
      <h1>错误语音播放测试页面</h1>
      <p class="description">
        此页面用于测试错误语音播放的重叠问题。当多个错误在短时间内（2秒内）连续推送时，
        系统应该停止当前播放，只播放最后一批错误的完整语音。
      </p>
    </div>
    
    <ErrorSoundTestPanel />
    
    <div class="usage-instructions">
      <h2>使用说明</h2>
      <div class="instruction-grid">
        <div class="instruction-card">
          <h3>🔄 重叠测试</h3>
          <p>模拟在2秒内推送3批错误数据：</p>
          <ul>
            <li>第1批：立即推送（3个错误）</li>
            <li>第2批：500ms后推送（3个错误）</li>
            <li>第3批：1000ms后推送（4个错误）</li>
          </ul>
          <p><strong>预期结果：</strong>只播放第3批的4个错误语音</p>
        </div>
        
        <div class="instruction-card">
          <h3>✅ 正常测试</h3>
          <p>模拟正常间隔（3秒）推送错误数据：</p>
          <ul>
            <li>第1批：立即推送（3个错误）</li>
            <li>第2批：3秒后推送（3个错误）</li>
            <li>第3批：6秒后推送（4个错误）</li>
          </ul>
          <p><strong>预期结果：</strong>每批错误都完整播放</p>
        </div>
        
        <div class="instruction-card">
          <h3>🎵 单个测试</h3>
          <p>测试单个错误的语音播放：</p>
          <ul>
            <li>播放一个"未双手作业"错误</li>
          </ul>
          <p><strong>预期结果：</strong>播放单个错误语音</p>
        </div>
        
        <div class="instruction-card">
          <h3>🛑 停止播放</h3>
          <p>手动停止当前播放：</p>
          <ul>
            <li>立即停止所有音频播放</li>
            <li>清空播放队列</li>
          </ul>
          <p><strong>预期结果：</strong>所有语音立即停止</p>
        </div>
      </div>
    </div>
    
    <div class="technical-details">
      <h2>技术实现</h2>
      <div class="detail-section">
        <h3>防重叠机制</h3>
        <p>
          当检测到新的错误推送时间与上次播放时间间隔小于2秒时，系统会：
        </p>
        <ol>
          <li>立即停止当前正在播放的语音</li>
          <li>清空当前播放队列</li>
          <li>使用最新的错误列表重新构建播放队列</li>
          <li>开始播放新的错误语音序列</li>
        </ol>
      </div>
      
      <div class="detail-section">
        <h3>播放逻辑</h3>
        <ul>
          <li><strong>单个错误：</strong>直接播放对应的错误语音</li>
          <li><strong>多个错误：</strong>按顺序依次播放每个错误的语音</li>
          <li><strong>无声音错误：</strong>自动过滤，不影响播放队列</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ErrorSoundTestPanel from '@/components/ErrorSoundTestPanel.vue';
</script>

<style scoped>
.error-sound-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  margin: 0 0 15px 0;
  font-size: 2.5em;
  font-weight: 300;
}

.description {
  font-size: 1.1em;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.usage-instructions {
  margin: 40px 0;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 12px;
}

.usage-instructions h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.instruction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.instruction-card {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.instruction-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.instruction-card h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.3em;
}

.instruction-card p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.instruction-card ul, .instruction-card ol {
  margin: 10px 0;
  padding-left: 20px;
  color: #666;
}

.instruction-card li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.instruction-card strong {
  color: #28a745;
  font-weight: 600;
}

.technical-details {
  margin: 40px 0;
  padding: 30px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
}

.technical-details h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.detail-section {
  margin-bottom: 25px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.detail-section h3 {
  margin: 0 0 15px 0;
  color: #007bff;
  font-size: 1.2em;
}

.detail-section p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.6;
}

.detail-section ul, .detail-section ol {
  margin: 10px 0;
  padding-left: 20px;
  color: #666;
}

.detail-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.detail-section strong {
  color: #333;
  font-weight: 600;
}

@media (max-width: 768px) {
  .error-sound-test-page {
    padding: 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 2em;
  }
  
  .instruction-grid {
    grid-template-columns: 1fr;
  }
  
  .usage-instructions, .technical-details {
    padding: 20px;
  }
}
</style>
