# 操作日志组件统一处理

## 问题描述

训练、考核、比赛三种模式的操作日志栏存在不一致的问题：

1. **组件冗余**：
   - 三种模式各自维护独立的OperationLog组件
   - 代码重复，维护成本高

2. **音频处理不一致**：
   - 训练模式：在上层处理音频播报
   - 考核模式：在操作日志组件内部处理音频
   - 比赛模式：在操作日志组件内部处理音频

3. **显示逻辑不一致**：
   - 部分组件固定显示"完成一次操作"
   - 部分组件智能显示具体操作内容

## 解决方案

### 1. 组件统一

删除冗余的操作日志组件，统一使用训练模式的OperationLog组件：

- ✅ 删除 `src/views/cddc/skill-assessment/components/OperationLog.vue`
- ✅ 删除 `src/views/cddc/skill-competition/components/OperationLog.vue`
- ✅ 保留 `src/views/cddc/training/components/OperationLog.vue` 作为统一组件
- ✅ 更新考核和比赛模式的导入路径

### 2. 音频处理统一

将音频处理逻辑提升到上层，保持与训练模式一致：

**训练模式（原有逻辑）：**
```typescript
// 在 index.vue 中处理音频
const { playErrorSoundSmart } = useSmartErrorSound();

const handleWebSocketMessage = (data) => {
  // 根据声音设置播放错误提示音
  if (soundEnabled.value && data.operationLog?.actionLabels?.length) {
    playErrorSoundSmart(
      data.operationLog.actionLabels,
      soundType.value as SoundType
    );
  }
};
```

**考核模式（新增逻辑）：**
```typescript
// 在 index.vue 中添加音频处理
import { useOperationLogErrorSound } from '/@/composables/useSmartErrorSound';

const { handleOperationLogError } = useOperationLogErrorSound();

const handleAssessmentDataUpdate = (data: AssessmentResponse) => {
  // ... 原有逻辑 ...

  // 处理音频播报
  if (data.operationLog) {
    handleOperationLogError(data.operationLog, 'rational');
  }
};
```

**比赛模式（新增逻辑）：**
```typescript
// 在 index.vue 中添加音频处理
import { useOperationLogErrorSound } from '/@/composables/useSmartErrorSound';

const { handleOperationLogError } = useOperationLogErrorSound();

const handleCompetitionDataUpdate = (data: CompetitionResponse) => {
  // ... 原有逻辑 ...

  // 处理音频播报
  if (data.operationLog) {
    handleOperationLogError(data.operationLog, 'rational');
  }
};
```

### 3. 操作日志组件简化

从统一的操作日志组件中移除音频处理逻辑，专注于日志显示：

```typescript
// 移除音频相关导入和逻辑
// import { useOperationLogErrorSound } from '/@/composables/useSmartErrorSound';

const logList = computed(() => {
  if (Object.keys(props.data).length && props.data.operationLog) {
    const newLog = props.data.operationLog;
    if (!localLogList.value.some((item) => item.id === newLog.id)) {
      localLogList.value = [newLog, ...localLogList.value];
      // 移除音频处理逻辑，由上层处理
    }
  }
  return localLogList.value;
});
```

### 4. 修复错误信息重复显示

解决左侧操作文本与右侧错误标签重复显示的问题：

```typescript
const getActionText = (item: any) => {
  // 只有合格的操作才显示"完成一次操作"
  // 不合格或不达标的操作不显示文本，避免与右侧标签重复
  if (item.result === 1) {
    return '完成一次操作';
  }

  // 不合格或不达标时返回空字符串，让右侧的错误标签来显示具体信息
  return '';
};
```

```vue
<!-- 模板中只在有文本时才显示操作文本元素 -->
<div class="log-action" v-if="getActionText(item)">{{ getActionText(item) }}</div>
```

## 修改的文件

### 删除的文件
- ❌ `src/views/cddc/skill-assessment/components/OperationLog.vue`
- ❌ `src/views/cddc/skill-competition/components/OperationLog.vue`

### 修改的文件

1. **`src/views/cddc/training/components/OperationLog.vue`**
   - 移除音频处理逻辑，专注于日志显示
   - 保留智能文本显示逻辑（`getActionText`方法）
   - 作为统一的操作日志组件

2. **`src/views/cddc/skill-assessment/index.vue`**
   - 更新导入路径：使用训练模式的OperationLog组件
   - 添加音频处理逻辑到WebSocket数据更新处理中
   - 导入`useOperationLogErrorSound`

3. **`src/views/cddc/skill-competition/index.vue`**
   - 更新导入路径：使用训练模式的OperationLog组件
   - 添加音频处理逻辑到WebSocket数据更新处理中
   - 导入`useOperationLogErrorSound`

## 统一后的效果

1. **组件统一**：
   - 只维护一个操作日志组件，减少代码冗余
   - 降低维护成本，提高代码一致性

2. **音频处理统一**：
   - 所有模式都在上层处理音频播报
   - 保持与训练模式一致的架构设计
   - 音频逻辑与UI逻辑分离

3. **智能文本显示**：
   - 合格操作：显示"完成一次操作"
   - 不合格/不达标操作：不显示左侧文本，只显示右侧错误标签
   - 避免错误信息重复显示，消除视觉冗余
   - 所有模式使用相同的显示逻辑

## 符合用户偏好

根据用户记忆中的偏好：
- ✅ 日志组件显示多条操作记录
- ✅ 右侧显示状态（合格、不合格、不达标三种状态）
- ✅ 在操作日志中，"完成一次操作"仅指合格状态
- ✅ 不合格和不达标状态显示具体的日志信息内容而不是通用文本
