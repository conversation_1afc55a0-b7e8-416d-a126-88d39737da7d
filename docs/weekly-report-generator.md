# 周报生成文档

## 使用说明

将此文档发给AI，并提供git提交记录，AI将根据以下规则和模板自动生成周报。

## 周报生成规则

### 1. 数据来源
- 基于git提交记录分析工作内容
- 提取提交时间范围内的所有commit信息
- 分析提交信息、修改文件和代码变更

### 2. 内容分析原则
- 根据commit message识别功能类型（feat/fix/perf/chore等）
- 将相关的多个commit合并为一个功能点
- 按照功能重要性和完成度进行分类
- 提取关键技术点和业务价值

### 3. 完成度评估
- 功能开发类：根据commit数量和代码变更量评估
- 问题修复类：一般标记为100%（已修复）
- 优化改进类：根据涉及范围评估完成度
- 新功能开发：根据功能完整性评估

### 4. 分类规则
按以下优先级对工作内容进行分类：
1. 新功能开发（feat）
2. 问题修复（fix）
3. 性能优化（perf）
4. 代码重构（refactor）
5. 文档更新（docs）
6. 其他改进（chore/style等）

## 周报模板

```
### 周报： YYYY-MM-DD ~ YYYY-MM-DD

#### 一、本周进展（附带完成比）

1. **功能名称** 完成度%
   - 具体实现内容1
   - 具体实现内容2
   - 技术要点或业务价值

2. **功能名称** 完成度%
   - 具体实现内容
   - 相关技术细节

[继续列举其他功能...]

#### 二、下周计划

1. 基于本周工作的后续计划
2. 新功能开发计划
3. 优化改进计划
4. 技术债务处理计划

#### 三、风险项
- 具体风险描述（如有）
- 暂无（如无风险）

---

**本周工作总结：**
简要总结本周工作重点、技术亮点和业务价值。
```

## 内容编写指导

### 功能名称命名规范
- 使用业务术语，避免纯技术术语
- 突出功能价值和用户体验
- 例如："智能错误提示音播放功能" 而不是 "useSmartErrorSound组件"

### 具体内容描述
- 重点描述实现的功能和解决的问题
- 包含关键技术点但以业务价值为主
- 避免过于技术化的描述

### 完成度评估标准
- 100%：功能完全实现并测试通过
- 90%：主要功能完成，少量细节待完善
- 80%：核心功能完成，部分辅助功能待开发
- 70%：基础框架完成，主要功能开发中
- 50%：需求分析和设计完成，开发进行中

### 下周计划制定原则
- 基于本周工作的自然延续
- 考虑技术债务和优化需求
- 平衡新功能开发和系统维护
- 预估合理的工作量

## 示例分析

### Git Commit 示例
```
feat: 添加智能错误提示音播放功能，优化错误音频播放逻辑
fix: 优化错误提示音播放逻辑，增加去重和防抖机制
fix: 优化错误提示音播放逻辑，调整防抖机制以避免重复播放相同错误
```

### 对应周报内容
```
1. **智能错误提示音播放功能开发** 100%
   - 新增智能错误提示音播放功能，优化错误音频播放逻辑
   - 添加去重和防抖机制，避免重复播放相同错误
   - 调整防抖机制以提升用户体验
```

## 注意事项

1. **时间范围**：确保提取指定时间范围内的所有提交记录
2. **作者过滤**：只统计指定作者的提交记录
3. **合并相关**：将功能相关的多个commit合并描述
4. **业务导向**：以业务价值和用户体验为描述重点
5. **客观准确**：基于实际代码变更，避免夸大或缩小工作量

## 使用方法

1. 将此文档提供给AI
2. 提供git提交记录（可通过以下命令获取）：
   ```bash
   git log --since="YYYY-MM-DD" --until="YYYY-MM-DD" --stat --pretty=format:"%h - %s (%an, %ad)" --date=short
   ```
3. AI将根据规则和模板自动生成周报
4. 根据需要对生成的周报进行微调
